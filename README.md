# 微信公众号发文作品回采系统

一个用于监控微信公众号发文动态的实时监控系统，可以添加多个公众号到监控列表，自动获取最新文章信息。

## 功能特性

- 🔍 **公众号搜索**: 输入公众号名称，验证并获取基本信息（名称、头像、描述）
- 📝 **监控列表管理**: 添加/删除公众号到监控列表
- 📰 **文章监控**: 实时监控公众号最新发文（标题、发布时间、摘要等）
- ⏰ **自动更新**: 每10分钟自动检查所有监控公众号的新文章
- 💾 **数据存储**: 使用SQLite本地数据库存储数据
- 🎨 **现代界面**: 基于Ant Design的美观用户界面

## 技术栈

### 前端
- React 18
- Vite
- Ant Design
- Axios

### 后端
- Node.js
- Express
- SQLite3
- Node-cron (定时任务)

## 安装和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd WXApp
```

### 2. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装服务端依赖
cd server
npm install

# 安装客户端依赖
cd ../client
npm install
```

### 3. 启动项目

#### 方式一：同时启动前后端
```bash
# 在根目录执行
npm run dev
```

#### 方式二：分别启动
```bash
# 启动后端服务器 (端口5000)
cd server
npm start

# 启动前端开发服务器 (端口3000)
cd client
npm run dev
```

### 4. 访问应用
打开浏览器访问: http://localhost:3000

### 5. 快速测试

#### API功能测试
```bash
node test-api.js
```

#### 三列式布局测试
```bash
node test-three-column-layout.js
```

#### 手动测试步骤
1. 在前端搜索框输入"人民日报"或"新华社"
2. 点击搜索，然后添加到监控列表
3. 点击公众号旁边的"刷新"按钮生成测试文章
4. 查看右侧文章列表是否正常显示
5. 尝试调整浏览器窗口大小测试响应式效果

## 使用说明

### 1. 添加公众号
1. 在搜索框中输入公众号名称（支持：人民日报、新华社、央视新闻等）
2. 点击搜索按钮或按回车键
3. 系统会显示找到的公众号信息（名称、头像、描述、认证状态）
4. 点击"添加到监控"按钮将公众号加入监控列表

### 2. 管理监控列表
- 左侧面板显示所有已添加的公众号
- 每个公众号显示头像、名称、描述和添加时间
- 点击"刷新"按钮手动更新某个公众号的文章
- 点击"删除"按钮移除不需要监控的公众号
- 支持实时显示监控公众号数量

### 3. 查看文章列表
- 右侧面板显示所有监控公众号的最新文章
- 文章按发布时间倒序排列，显示相对时间（如"2小时前"）
- 每篇文章显示：标题、摘要、发布公众号、发布时间
- 点击文章标题可跳转到原文（新窗口打开）
- 支持实时显示文章总数

### 4. 自动监控
- 系统每10分钟自动检查一次所有公众号的更新
- 新文章会自动添加到数据库，避免重复
- 可在文章列表中实时查看最新内容
- 支持手动刷新获取最新数据

### 5. 界面特性
- **三列式布局**: 专为PC端设计，添加公众号(29%) + 监控列表(33%) + 文章列表(38%)
- **功能分离**: 每列承担独立功能，操作流程清晰直观
- **空间优化**: 充分利用宽屏优势，信息密度更高
- **现代化UI**: 使用Ant Design组件库，卡片式设计，优雅的悬停效果
- **实时更新**: 无需刷新页面，支持热重载和实时数据更新
- **PC端专用**: 针对1920px+宽屏优化，提供最佳桌面体验
- **交互优化**: 优雅的加载状态、错误提示和操作反馈

## 项目结构

```
WXApp/
├── client/                 # 前端项目
│   ├── src/
│   │   ├── App.jsx        # 主应用组件
│   │   ├── main.jsx       # 入口文件
│   │   └── index.css      # 样式文件
│   ├── package.json
│   └── vite.config.js
├── server/                 # 后端项目
│   ├── index.js           # 服务器入口
│   ├── database.js        # 数据库操作
│   ├── wechat-service.js  # 微信服务
│   ├── package.json
│   └── .env               # 环境配置
├── package.json           # 根项目配置
└── README.md
```

## API 接口

### 搜索公众号
```
POST /api/search-account
Body: { "name": "公众号名称" }
```

### 获取监控列表
```
GET /api/accounts
```

### 添加公众号
```
POST /api/accounts
Body: { "name": "名称", "avatar": "头像URL", "description": "描述", "biz": "biz参数" }
```

### 删除公众号
```
DELETE /api/accounts/:id
```

### 获取文章列表
```
GET /api/articles?account_id=1&limit=50
```

### 手动刷新公众号
```
POST /api/refresh/:id
```

## 注意事项

1. **数据来源**: 由于微信公众号的反爬虫机制，当前版本使用模拟数据进行演示
2. **实际部署**: 在生产环境中需要考虑合法的数据获取方式
3. **数据库**: SQLite数据库文件会在首次运行时自动创建
4. **端口配置**: 默认后端端口5000，前端端口3000，可在配置文件中修改

## 开发说明

### 环境变量
在 `server/.env` 文件中可以配置：
- `PORT`: 服务器端口（默认5000）
- `MONITOR_INTERVAL`: 监控间隔（分钟，默认10）
- `MAX_ARTICLES_PER_ACCOUNT`: 每个公众号最大文章数（默认10）

### 数据库表结构
- `accounts`: 公众号信息表
- `articles`: 文章信息表
- `monitor_logs`: 监控日志表

## 许可证

MIT License
