// 布局测试脚本 - 添加更多测试数据
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function addTestData() {
  console.log('🎨 开始添加测试数据以验证布局优化...\n');

  try {
    // 添加多个公众号进行测试
    const accounts = [
      {
        name: '人民日报',
        avatar: 'https://via.placeholder.com/100x100?text=人民日报',
        description: '人民日报官方微信公众号，传播权威资讯',
        biz: 'MjM5MjQ2MjU2MA=='
      },
      {
        name: '新华社',
        avatar: 'https://via.placeholder.com/100x100?text=新华社',
        description: '新华社官方微信公众号，国家通讯社',
        biz: 'MjM5MjQ2MjU2MQ=='
      },
      {
        name: '央视新闻',
        avatar: 'https://via.placeholder.com/100x100?text=央视新闻',
        description: '央视新闻官方微信公众号，权威新闻资讯',
        biz: 'MjM5MjQ2MjU2Mg=='
      },
      {
        name: '澎湃新闻',
        avatar: 'https://via.placeholder.com/100x100?text=澎湃新闻',
        description: '澎湃新闻官方微信公众号，专业新闻报道',
        biz: 'MjM5MjQ2MjU2Mw=='
      }
    ];

    console.log('1. 添加测试公众号...');
    for (const account of accounts) {
      try {
        const result = await axios.post('/api/accounts', account);
        console.log(`✅ 添加成功: ${result.data.name}`);
        
        // 为每个公众号生成文章
        console.log(`   正在为 ${result.data.name} 生成测试文章...`);
        const refreshResult = await axios.post(`/api/refresh/${result.data.id}`);
        console.log(`   ✅ 生成了 ${refreshResult.data.newArticlesCount} 篇文章`);
        
      } catch (error) {
        if (error.response?.data?.error?.includes('已在监控列表中')) {
          console.log(`ℹ️  ${account.name} 已存在，跳过添加`);
          
          // 获取现有账号ID并刷新
          const accountsResult = await axios.get('/api/accounts');
          const existingAccount = accountsResult.data.find(acc => acc.name === account.name);
          if (existingAccount) {
            const refreshResult = await axios.post(`/api/refresh/${existingAccount.id}`);
            console.log(`   ✅ 刷新了 ${existingAccount.name}，生成 ${refreshResult.data.newArticlesCount} 篇新文章`);
          }
        } else {
          console.error(`❌ 添加 ${account.name} 失败:`, error.message);
        }
      }
    }

    // 检查最终状态
    console.log('\n2. 检查最终状态...');
    const finalAccounts = await axios.get('/api/accounts');
    const finalArticles = await axios.get('/api/articles?limit=20');
    
    console.log(`✅ 总共监控 ${finalAccounts.data.length} 个公众号:`);
    finalAccounts.data.forEach(account => {
      console.log(`   - ${account.name}: ${account.description}`);
    });
    
    console.log(`\n✅ 总共有 ${finalArticles.data.length} 篇文章可供显示`);
    console.log('\n🎉 测试数据添加完成！');
    console.log('\n📱 请在浏览器中访问 http://localhost:3000 查看优化后的布局效果');
    console.log('💡 建议测试不同屏幕尺寸下的显示效果');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
addTestData();
