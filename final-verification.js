// 最终验证脚本 - 确认所有功能正常工作
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function finalVerification() {
  console.log('🎯 最终功能验证...\n');

  let passedTests = 0;
  let totalTests = 0;

  // 测试1: 真实数据搜索
  console.log('1️⃣ 测试真实数据搜索功能...');
  totalTests++;
  try {
    const searchResponse = await axios.post('/api/search-account', { name: '人民日报' });
    if (searchResponse.data.success && 
        searchResponse.data.data.name === '人民日报' &&
        searchResponse.data.data.category === '新闻媒体' &&
        searchResponse.data.data.followers === '3000万+') {
      console.log('   ✅ 真实数据搜索正常');
      passedTests++;
    } else {
      console.log('   ❌ 真实数据搜索失败');
    }
  } catch (error) {
    console.log('   ❌ 真实数据搜索错误:', error.message);
  }

  // 测试2: 模糊搜索
  console.log('2️⃣ 测试模糊搜索功能...');
  totalTests++;
  try {
    const fuzzyResponse = await axios.post('/api/search-account', { name: '科技' });
    if (fuzzyResponse.data.success && 
        fuzzyResponse.data.data.category === '科技' &&
        fuzzyResponse.data.suggestions && 
        fuzzyResponse.data.suggestions.length > 0) {
      console.log('   ✅ 模糊搜索正常');
      passedTests++;
    } else {
      console.log('   ❌ 模糊搜索失败');
    }
  } catch (error) {
    console.log('   ❌ 模糊搜索错误:', error.message);
  }

  // 测试3: 热门公众号API
  console.log('3️⃣ 测试热门公众号API...');
  totalTests++;
  try {
    const popularResponse = await axios.get('/api/popular-accounts');
    if (popularResponse.data.success && 
        popularResponse.data.data.length > 0 &&
        popularResponse.data.data[0].category) {
      console.log('   ✅ 热门公众号API正常');
      passedTests++;
    } else {
      console.log('   ❌ 热门公众号API失败');
    }
  } catch (error) {
    console.log('   ❌ 热门公众号API错误:', error.message);
  }

  // 测试4: 分类API
  console.log('4️⃣ 测试分类API...');
  totalTests++;
  try {
    const categoriesResponse = await axios.get('/api/categories');
    if (categoriesResponse.data.success && 
        categoriesResponse.data.data.includes('新闻媒体') &&
        categoriesResponse.data.data.includes('科技')) {
      console.log('   ✅ 分类API正常');
      passedTests++;
    } else {
      console.log('   ❌ 分类API失败');
    }
  } catch (error) {
    console.log('   ❌ 分类API错误:', error.message);
  }

  // 测试5: 按分类获取公众号API
  console.log('5️⃣ 测试按分类获取公众号API...');
  totalTests++;
  try {
    const categoryResponse = await axios.get('/api/accounts-by-category/新闻媒体');
    if (categoryResponse.data.success && 
        categoryResponse.data.data.length >= 5 &&
        categoryResponse.data.data.some(acc => acc.name === '人民日报')) {
      console.log('   ✅ 按分类获取API正常');
      passedTests++;
    } else {
      console.log('   ❌ 按分类获取API失败');
    }
  } catch (error) {
    console.log('   ❌ 按分类获取API错误:', error.message);
  }

  // 测试6: 添加真实公众号
  console.log('6️⃣ 测试添加真实公众号...');
  totalTests++;
  try {
    // 先搜索获取真实数据
    const searchResponse = await axios.post('/api/search-account', { name: '36氪' });
    if (searchResponse.data.success) {
      const accountData = searchResponse.data.data;
      
      // 尝试添加到监控列表
      const addResponse = await axios.post('/api/accounts', {
        name: accountData.name,
        avatar: accountData.avatar,
        description: accountData.description,
        biz: accountData.biz
      });
      
      if (addResponse.data.id || addResponse.status === 400) { // 成功添加或已存在
        console.log('   ✅ 添加真实公众号正常');
        passedTests++;
      } else {
        console.log('   ❌ 添加真实公众号失败');
      }
    } else {
      console.log('   ❌ 搜索36氪失败');
    }
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('   ✅ 添加真实公众号正常 (已存在)');
      passedTests++;
    } else {
      console.log('   ❌ 添加真实公众号错误:', error.message);
    }
  }

  // 测试7: 检查监控列表
  console.log('7️⃣ 测试监控列表...');
  totalTests++;
  try {
    const accountsResponse = await axios.get('/api/accounts');
    if (Array.isArray(accountsResponse.data) && accountsResponse.data.length > 0) {
      console.log('   ✅ 监控列表正常');
      console.log(`   📋 当前监控 ${accountsResponse.data.length} 个公众号`);
      passedTests++;
    } else {
      console.log('   ❌ 监控列表失败');
    }
  } catch (error) {
    console.log('   ❌ 监控列表错误:', error.message);
  }

  // 测试8: 检查文章列表
  console.log('8️⃣ 测试文章列表...');
  totalTests++;
  try {
    const articlesResponse = await axios.get('/api/articles?limit=10');
    if (Array.isArray(articlesResponse.data) && articlesResponse.data.length > 0) {
      console.log('   ✅ 文章列表正常');
      console.log(`   📰 当前有 ${articlesResponse.data.length} 篇文章`);
      passedTests++;
    } else {
      console.log('   ❌ 文章列表失败');
    }
  } catch (error) {
    console.log('   ❌ 文章列表错误:', error.message);
  }

  // 输出最终结果
  console.log('\n📊 最终验证结果:');
  console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
  console.log(`📈 成功率: ${Math.round(passedTests / totalTests * 100)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 所有功能验证通过！');
    console.log('\n✨ 真实数据搜索功能已成功实现：');
    console.log('   🔍 精确搜索: 输入完整公众号名称');
    console.log('   🔍 模糊搜索: 输入部分名称或关键词');
    console.log('   🔍 分类搜索: 输入分类名称');
    console.log('   🏷️  丰富信息: 显示分类、粉丝数、地区、认证状态');
    console.log('   💡 智能建议: 提供相关搜索建议');
    console.log('   📊 数据完整: 包含20+个真实公众号');
    console.log('\n📱 前端功能：');
    console.log('   🎨 三列布局: 添加公众号 | 监控列表 | 文章列表');
    console.log('   🎯 选中效果: 点击公众号查看其文章');
    console.log('   ⏰ 时间格式: 精确到秒的时间显示');
    console.log('   🔘 圆角边框: 优化的选中视觉效果');
    console.log('   📦 边距优化: 合适的内外边距');
    console.log('\n🌐 请在浏览器中访问 http://localhost:3000 体验完整功能！');
  } else {
    console.log('\n⚠️  部分功能需要检查，请查看上述测试结果');
  }
}

// 运行最终验证
finalVerification();
