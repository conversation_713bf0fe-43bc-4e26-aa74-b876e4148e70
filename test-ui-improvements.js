// UI改进测试脚本 - 监控列表边距、圆角和时间格式
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testUIImprovements() {
  console.log('🎨 开始测试UI改进效果...\n');

  try {
    // 1. 检查现有数据
    console.log('1. 检查现有数据...');
    const accountsResult = await axios.get('/api/accounts');
    const accounts = accountsResult.data;
    
    if (accounts.length === 0) {
      console.log('❌ 没有监控的公众号，请先添加一些公众号');
      return;
    }
    
    console.log(`✅ 找到 ${accounts.length} 个监控的公众号`);

    // 2. 刷新一些公众号的文章，确保有不同时间的数据
    console.log('\n2. 刷新公众号文章，生成测试数据...');
    for (let i = 0; i < Math.min(3, accounts.length); i++) {
      const account = accounts[i];
      try {
        const refreshResult = await axios.post(`/api/refresh/${account.id}`);
        console.log(`✅ 刷新 "${account.name}": ${refreshResult.data.newArticlesCount} 篇新文章`);
        
        // 间隔一下，避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.log(`⚠️  刷新 "${account.name}" 失败: ${error.message}`);
      }
    }

    // 3. 获取文章数据，检查时间格式
    console.log('\n3. 检查文章时间格式...');
    const articlesResult = await axios.get('/api/articles?limit=15');
    const articles = articlesResult.data;
    
    console.log(`✅ 获取到 ${articles.length} 篇文章`);
    console.log('📅 时间格式示例:');
    
    // 显示不同时间格式的示例
    articles.slice(0, 8).forEach((article, index) => {
      const publishTime = new Date(article.publish_time);
      const now = new Date();
      const diff = now - publishTime;
      const days = Math.floor(diff / ********);
      
      let timeFormat = '';
      if (days === 0) {
        timeFormat = publishTime.toLocaleTimeString('zh-CN', { 
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } else if (days === 1) {
        timeFormat = '昨天 ' + publishTime.toLocaleTimeString('zh-CN', { 
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } else {
        timeFormat = publishTime.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
      }
      
      console.log(`   ${index + 1}. ${article.title.substring(0, 30)}...`);
      console.log(`      原始时间: ${article.publish_time}`);
      console.log(`      显示格式: ${timeFormat}`);
      console.log(`      来源: ${article.account_name}`);
      console.log('');
    });

    console.log('\n🎨 UI改进说明:');
    console.log('   📦 监控列表边距优化:');
    console.log('     ✓ 列表项内边距: 16px 20px (增加左右边距)');
    console.log('     ✓ 列表项外边距: 6px 12px (增加上下和左右边距)');
    console.log('     ✓ 列表容器内边距: 8px 0 (顶部和底部留白)');

    console.log('\n   🔘 选中效果圆角优化:');
    console.log('     ✓ 整个列表项圆角: 8px (四个角都有圆角)');
    console.log('     ✓ 选中状态: 2px蓝色边框 + 淡蓝色背景');
    console.log('     ✓ 悬停效果: 2px灰色边框 + 灰色背景 + 轻微上移');
    console.log('     ✓ 选中项阴影: 蓝色阴影增强视觉效果');

    console.log('\n   ⏰ 时间格式优化:');
    console.log('     ✓ 今天的文章: 显示 "HH:mm:ss" (如: 14:30:25)');
    console.log('     ✓ 昨天的文章: 显示 "昨天 HH:mm:ss" (如: 昨天 09:15:30)');
    console.log('     ✓ 更早的文章: 显示完整日期时间 (如: 2025/06/24 16:45:12)');
    console.log('     ✓ 24小时制格式，精确到秒');

    console.log('\n   🎯 视觉效果增强:');
    console.log('     ✓ 悬停时列表项轻微上移 (translateY(-1px))');
    console.log('     ✓ 选中项有特殊阴影效果');
    console.log('     ✓ 所有动画过渡时间 0.2s');
    console.log('     ✓ 更好的视觉层次和间距');

    console.log('\n📱 请在浏览器中访问 http://localhost:3000');
    console.log('🔍 测试要点:');
    console.log('   1. 监控列表项之间有明显间距');
    console.log('   2. 选中效果是整个列表项的圆角边框');
    console.log('   3. 悬停时有轻微上移动画效果');
    console.log('   4. 文章时间显示精确到秒');
    console.log('   5. 今天、昨天、更早的文章时间格式不同');

    console.log('\n💡 建议测试场景:');
    console.log('   - 在不同公众号之间切换，观察选中效果');
    console.log('   - 鼠标悬停在列表项上，观察动画效果');
    console.log('   - 查看不同时间的文章，确认时间格式');
    console.log('   - 在1920px宽屏上测试最佳效果');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testUIImprovements();
