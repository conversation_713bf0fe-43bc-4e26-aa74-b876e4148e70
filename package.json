{"name": "wx-article-monitor", "version": "1.0.0", "description": "微信公众号发文作品回采系统", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["微信", "公众号", "监控", "爬虫"], "author": "", "license": "MIT", "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "concurrently": "^8.2.2", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "nodemon": "^3.1.10", "vite": "^7.0.0"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.2", "axios": "^1.10.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "node-cron": "^4.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "rss-parser": "^3.13.0", "sqlite3": "^5.1.7"}}