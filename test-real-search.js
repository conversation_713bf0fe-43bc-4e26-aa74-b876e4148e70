// 测试真实公众号搜索功能
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testRealSearch() {
  console.log('🔍 测试真实公众号搜索功能...\n');

  const testCases = [
    // 精确匹配测试
    { query: '人民日报', expected: true, description: '精确匹配 - 人民日报' },
    { query: '新华社', expected: true, description: '精确匹配 - 新华社' },
    { query: '央视新闻', expected: true, description: '精确匹配 - 央视新闻' },
    
    // 模糊匹配测试
    { query: '人民', expected: true, description: '模糊匹配 - 人民' },
    { query: '新华', expected: true, description: '模糊匹配 - 新华' },
    { query: '央视', expected: true, description: '模糊匹配 - 央视' },
    { query: '科技', expected: true, description: '模糊匹配 - 科技' },
    { query: '财经', expected: true, description: '模糊匹配 - 财经' },
    
    // 分类匹配测试
    { query: '新闻', expected: true, description: '分类匹配 - 新闻' },
    { query: '汽车', expected: true, description: '分类匹配 - 汽车' },
    { query: '教育', expected: true, description: '分类匹配 - 教育' },
    
    // 无匹配测试
    { query: '不存在的公众号', expected: false, description: '无匹配测试' },
    { query: 'xyz123', expected: false, description: '无匹配测试 - 随机字符' }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    try {
      console.log(`📝 测试: ${testCase.description}`);
      console.log(`   搜索词: "${testCase.query}"`);
      
      const response = await axios.post('/api/search-account', { name: testCase.query });
      const success = response.data.success;
      
      if (success === testCase.expected) {
        console.log(`   ✅ 通过 - 返回结果符合预期`);
        
        if (success) {
          const account = response.data.data;
          console.log(`   📋 找到公众号:`);
          console.log(`      名称: ${account.name}`);
          console.log(`      描述: ${account.description}`);
          console.log(`      分类: ${account.category || '未知'}`);
          console.log(`      粉丝: ${account.followers || '未知'}`);
          console.log(`      地区: ${account.location || '未知'}`);
          console.log(`      认证: ${account.verified ? '是' : '否'}`);
          
          if (response.data.suggestions && response.data.suggestions.length > 0) {
            console.log(`   💡 其他建议: ${response.data.suggestions.slice(0, 3).map(s => s.name).join(', ')}`);
          }
        } else {
          console.log(`   📋 未找到匹配结果`);
          if (response.data.suggestions && response.data.suggestions.length > 0) {
            console.log(`   💡 建议搜索: ${response.data.suggestions.slice(0, 3).map(s => s.name).join(', ')}`);
          }
        }
        
        passedTests++;
      } else {
        console.log(`   ❌ 失败 - 预期 ${testCase.expected ? '成功' : '失败'}，实际 ${success ? '成功' : '失败'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 错误 - ${error.message}`);
    }
    
    console.log(''); // 空行分隔
  }

  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️  部分测试失败，请检查实现');
  }

  // 测试新增的API端点
  console.log('\n🔧 测试新增API端点...\n');

  try {
    // 测试热门公众号
    console.log('📈 测试热门公众号API...');
    const popularResponse = await axios.get('/api/popular-accounts');
    if (popularResponse.data.success) {
      console.log(`✅ 获取到 ${popularResponse.data.data.length} 个热门公众号:`);
      popularResponse.data.data.slice(0, 5).forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.name} (${account.category}) - ${account.followers}`);
      });
    } else {
      console.log('❌ 获取热门公众号失败');
    }

    // 测试分类API
    console.log('\n📂 测试分类API...');
    const categoriesResponse = await axios.get('/api/categories');
    if (categoriesResponse.data.success) {
      console.log(`✅ 获取到 ${categoriesResponse.data.data.length} 个分类:`);
      console.log(`   ${categoriesResponse.data.data.join(', ')}`);
    } else {
      console.log('❌ 获取分类失败');
    }

    // 测试按分类获取公众号
    console.log('\n📋 测试按分类获取公众号...');
    const categoryResponse = await axios.get('/api/accounts-by-category/新闻媒体');
    if (categoryResponse.data.success) {
      console.log(`✅ 新闻媒体分类下有 ${categoryResponse.data.data.length} 个公众号:`);
      categoryResponse.data.data.forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.name} - ${account.followers}`);
      });
    } else {
      console.log('❌ 按分类获取公众号失败');
    }

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
  }

  console.log('\n🎯 功能特点总结:');
  console.log('✅ 精确匹配: 输入完整公众号名称，返回精确结果');
  console.log('✅ 模糊匹配: 输入部分名称，返回相关结果');
  console.log('✅ 分类搜索: 输入分类关键词，返回该分类下的公众号');
  console.log('✅ 智能建议: 未找到结果时，提供相关建议');
  console.log('✅ 详细信息: 显示分类、粉丝数、地区、认证状态等');
  console.log('✅ 排序优化: 按匹配度、认证状态、粉丝数排序');

  console.log('\n📱 前端显示增强:');
  console.log('🏷️  分类标签: 绿色标签显示公众号分类');
  console.log('👥 粉丝数量: 橙色标签显示粉丝规模');
  console.log('📍 地理位置: 紫色标签显示所在地区');
  console.log('✅ 认证状态: 蓝色标签显示官方认证');

  console.log('\n🔍 使用建议:');
  console.log('1. 搜索知名媒体: 人民日报、新华社、央视新闻');
  console.log('2. 搜索科技类: 36氪、虎嗅APP、科技日报');
  console.log('3. 搜索财经类: 财经杂志、第一财经');
  console.log('4. 搜索生活类: 丁香医生、十点读书、下厨房');
  console.log('5. 搜索汽车类: 汽车之家、懂车帝');
  console.log('6. 模糊搜索: 输入"科技"、"财经"、"汽车"等关键词');

  console.log('\n📊 数据库包含:');
  console.log('📰 新闻媒体: 5个权威媒体公众号');
  console.log('💻 科技类: 3个知名科技媒体');
  console.log('💰 财经类: 2个专业财经媒体');
  console.log('🏥 健康类: 1个专业医疗科普');
  console.log('📚 文化类: 1个知名读书平台');
  console.log('🚗 汽车类: 2个主流汽车媒体');
  console.log('🎓 教育类: 2个知名教育机构');
  console.log('🍽️  美食类: 1个美食菜谱平台');
  console.log('✈️  旅游类: 1个旅游攻略平台');
  console.log('👗 时尚类: 1个时尚杂志');
  console.log('🎬 娱乐类: 1个娱乐资讯账号');
}

// 运行测试
testRealSearch();
