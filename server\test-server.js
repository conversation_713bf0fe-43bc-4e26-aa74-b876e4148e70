const express = require('express');
const cors = require('cors');
const RSSService = require('./rss-service');

const app = express();
const PORT = 5001; // 使用不同端口避免冲突

// 中间件
app.use(cors());
app.use(express.json());

// 初始化RSS服务
const rssService = new RSSService();

// 测试路由
app.get('/test', (req, res) => {
  console.log('收到测试请求');
  res.json({ message: '服务器工作正常' });
});

// RSS测试路由
app.get('/api/rss/test', (req, res) => {
  console.log('收到RSS测试请求');
  res.json({ message: 'RSS API工作正常' });
});

// 获取推荐的RSS源
app.get('/api/rss/recommended', async (req, res) => {
  try {
    console.log('收到RSS推荐请求');
    const feeds = rssService.getRecommendedRSSFeeds();
    console.log('获取到推荐RSS源:', feeds.length);
    res.json({ success: true, data: feeds });
  } catch (error) {
    console.error('获取推荐RSS失败:', error);
    res.status(500).json({ error: '获取推荐RSS失败' });
  }
});

// 验证RSS URL
app.post('/api/rss/validate', async (req, res) => {
  try {
    const { url } = req.body;
    console.log('收到RSS验证请求:', url);

    if (!url) {
      return res.status(400).json({ error: 'RSS URL不能为空' });
    }

    const result = await rssService.validateRSSUrl(url);
    console.log('RSS验证结果:', result);
    res.json(result);
  } catch (error) {
    console.error('RSS验证失败:', error);
    res.status(500).json({ error: 'RSS验证失败' });
  }
});

// 添加RSS订阅（演示版本）
app.post('/api/rss/subscribe', async (req, res) => {
  try {
    const { name, rssUrl, description, category } = req.body;
    console.log('收到RSS订阅请求:', { name, rssUrl, description, category });

    if (!name || !rssUrl) {
      return res.status(400).json({ error: '名称和RSS URL不能为空' });
    }

    // 验证RSS URL
    const validation = await rssService.validateRSSUrl(rssUrl);
    if (!validation.valid) {
      return res.status(400).json({ error: `RSS URL无效: ${validation.error}` });
    }

    // 模拟成功添加
    const result = {
      success: true,
      message: 'RSS订阅添加成功',
      data: {
        id: Date.now(),
        name,
        rssUrl,
        description: description || validation.description || `${name}的RSS订阅`,
        category: category || '其他',
        created_at: new Date().toISOString()
      }
    };

    console.log('RSS订阅添加成功:', result.data);
    res.json(result);
  } catch (error) {
    console.error('添加RSS订阅失败:', error);
    res.status(500).json({ error: '添加RSS订阅失败' });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`测试服务器运行在端口 ${PORT}`);
  console.log(`测试地址: http://localhost:${PORT}/test`);
  console.log(`RSS API地址: http://localhost:${PORT}/api/rss/test`);
});
