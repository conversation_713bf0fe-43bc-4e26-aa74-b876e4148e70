// 测试服务器搜索功能
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testServerSearch() {
  console.log('🔍 测试服务器搜索功能...\n');

  const testCases = [
    { name: '人民日报', description: '精确匹配 - 人民日报' },
    { name: '新华社', description: '精确匹配 - 新华社' },
    { name: '央视新闻', description: '精确匹配 - 央视新闻' },
    { name: '36氪', description: '精确匹配 - 36氪' },
    { name: '科技', description: '分类匹配 - 科技' },
    { name: '新闻', description: '分类匹配 - 新闻' }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📝 ${testCase.description}`);
      console.log(`   搜索词: "${testCase.name}"`);
      
      const response = await axios.post('/api/search-account', { 
        name: testCase.name 
      }, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });
      
      console.log(`   状态: ${response.data.success ? '成功' : '失败'}`);
      
      if (response.data.success) {
        const account = response.data.data;
        console.log(`   结果: ${account.name}`);
        console.log(`   描述: ${account.description}`);
        console.log(`   分类: ${account.category || '未知'}`);
        console.log(`   粉丝: ${account.followers || '未知'}`);
        console.log(`   地区: ${account.location || '未知'}`);
        console.log(`   认证: ${account.verified ? '是' : '否'}`);
        
        if (response.data.suggestions && response.data.suggestions.length > 0) {
          console.log(`   建议: ${response.data.suggestions.slice(0, 3).map(s => s.name).join(', ')}`);
        }
      } else {
        console.log(`   消息: ${response.data.message || '未知错误'}`);
        if (response.data.suggestions && response.data.suggestions.length > 0) {
          console.log(`   建议: ${response.data.suggestions.slice(0, 3).map(s => s.name).join(', ')}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
      if (error.response) {
        console.log(`   响应: ${JSON.stringify(error.response.data)}`);
      }
    }
    
    console.log(''); // 空行分隔
  }

  // 测试新的API端点
  console.log('🔧 测试新增API端点...\n');

  try {
    // 测试热门公众号
    console.log('📈 测试热门公众号API...');
    const popularResponse = await axios.get('/api/popular-accounts');
    console.log(`   状态: ${popularResponse.status}`);
    console.log(`   成功: ${popularResponse.data.success}`);
    if (popularResponse.data.success) {
      console.log(`   数量: ${popularResponse.data.data.length}`);
      popularResponse.data.data.slice(0, 3).forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.name} (${account.category}) - ${account.followers}`);
      });
    }
  } catch (error) {
    console.log(`   ❌ 热门公众号API错误: ${error.message}`);
  }

  console.log('');

  try {
    // 测试分类API
    console.log('📂 测试分类API...');
    const categoriesResponse = await axios.get('/api/categories');
    console.log(`   状态: ${categoriesResponse.status}`);
    console.log(`   成功: ${categoriesResponse.data.success}`);
    if (categoriesResponse.data.success) {
      console.log(`   分类: ${categoriesResponse.data.data.join(', ')}`);
    }
  } catch (error) {
    console.log(`   ❌ 分类API错误: ${error.message}`);
  }

  console.log('');

  try {
    // 测试按分类获取公众号
    console.log('📋 测试按分类获取公众号...');
    const categoryResponse = await axios.get('/api/accounts-by-category/新闻媒体');
    console.log(`   状态: ${categoryResponse.status}`);
    console.log(`   成功: ${categoryResponse.data.success}`);
    if (categoryResponse.data.success) {
      console.log(`   数量: ${categoryResponse.data.data.length}`);
      categoryResponse.data.data.slice(0, 3).forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.name} - ${account.followers}`);
      });
    }
  } catch (error) {
    console.log(`   ❌ 按分类获取API错误: ${error.message}`);
  }

  console.log('\n🎯 测试完成！');
}

// 运行测试
testServerSearch();
