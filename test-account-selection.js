// 公众号选中和文章筛选功能测试脚本
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testAccountSelection() {
  console.log('🎯 开始测试公众号选中和文章筛选功能...\n');

  try {
    // 1. 检查现有公众号
    console.log('1. 检查监控列表...');
    const accountsResult = await axios.get('/api/accounts');
    const accounts = accountsResult.data;
    
    if (accounts.length === 0) {
      console.log('❌ 没有监控的公众号，请先添加一些公众号');
      return;
    }
    
    console.log(`✅ 找到 ${accounts.length} 个监控的公众号:`);
    accounts.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.name} (ID: ${account.id})`);
    });

    // 2. 测试默认选中第一个公众号的文章
    console.log('\n2. 测试默认选中第一个公众号的文章...');
    const firstAccountId = accounts[0].id;
    const firstAccountArticles = await axios.get(`/api/articles?limit=10&account_id=${firstAccountId}`);
    
    console.log(`✅ 第一个公众号 "${accounts[0].name}" 的文章:`);
    console.log(`   共 ${firstAccountArticles.data.length} 篇文章`);
    firstAccountArticles.data.slice(0, 3).forEach((article, index) => {
      console.log(`   ${index + 1}. ${article.title}`);
      console.log(`      封面: ${article.cover_url ? '有' : '无'}`);
      console.log(`      时间: ${new Date(article.publish_time).toLocaleString()}`);
    });

    // 3. 测试选中第二个公众号的文章（如果存在）
    if (accounts.length > 1) {
      console.log('\n3. 测试选中第二个公众号的文章...');
      const secondAccountId = accounts[1].id;
      const secondAccountArticles = await axios.get(`/api/articles?limit=10&account_id=${secondAccountId}`);
      
      console.log(`✅ 第二个公众号 "${accounts[1].name}" 的文章:`);
      console.log(`   共 ${secondAccountArticles.data.length} 篇文章`);
      secondAccountArticles.data.slice(0, 3).forEach((article, index) => {
        console.log(`   ${index + 1}. ${article.title}`);
        console.log(`      封面: ${article.cover_url ? '有' : '无'}`);
        console.log(`      时间: ${new Date(article.publish_time).toLocaleString()}`);
      });
    }

    // 4. 测试获取所有文章（不筛选）
    console.log('\n4. 测试获取所有文章（不筛选）...');
    const allArticles = await axios.get('/api/articles?limit=10');
    console.log(`✅ 所有公众号的文章: 共 ${allArticles.data.length} 篇`);
    
    // 统计每个公众号的文章数量
    const articlesByAccount = {};
    allArticles.data.forEach(article => {
      const accountName = article.account_name;
      articlesByAccount[accountName] = (articlesByAccount[accountName] || 0) + 1;
    });
    
    console.log('   按公众号分布:');
    Object.entries(articlesByAccount).forEach(([accountName, count]) => {
      console.log(`     ${accountName}: ${count} 篇`);
    });

    // 5. 刷新一个公众号的文章
    console.log('\n5. 测试刷新公众号文章...');
    const refreshResult = await axios.post(`/api/refresh/${firstAccountId}`);
    console.log(`✅ 刷新 "${accounts[0].name}" 成功:`);
    console.log(`   新增文章: ${refreshResult.data.newArticlesCount} 篇`);
    console.log(`   总文章数: ${refreshResult.data.totalArticles} 篇`);

    // 6. 验证刷新后的文章数量
    console.log('\n6. 验证刷新后的文章数量...');
    const updatedArticles = await axios.get(`/api/articles?limit=10&account_id=${firstAccountId}`);
    console.log(`✅ 刷新后 "${accounts[0].name}" 的文章: ${updatedArticles.data.length} 篇`);

    console.log('\n🎯 功能测试说明:');
    console.log('   📱 前端功能:');
    console.log('     ✓ 默认选中第一个公众号');
    console.log('     ✓ 点击公众号切换选中状态');
    console.log('     ✓ 选中状态有视觉反馈（蓝色边框）');
    console.log('     ✓ 文章列表只显示选中公众号的文章');
    console.log('     ✓ 文章显示封面图、标题、发布时间');
    console.log('     ✓ 移除了公众号名称和摘要显示');

    console.log('\n   🔧 后端功能:');
    console.log('     ✓ API支持按account_id筛选文章');
    console.log('     ✓ 文章包含封面图URL');
    console.log('     ✓ 刷新功能正常工作');
    console.log('     ✓ 数据库查询优化');

    console.log('\n   🎨 UI优化:');
    console.log('     ✓ 监控列表选中效果：左侧蓝色边框');
    console.log('     ✓ 选中项背景色：淡蓝色');
    console.log('     ✓ 悬停效果：灰色背景');
    console.log('     ✓ 文章列表：80x60px封面图');
    console.log('     ✓ 标题限制2行显示');
    console.log('     ✓ 只显示发布时间，移除公众号名称');

    console.log('\n📱 请在浏览器中访问 http://localhost:3000');
    console.log('🔍 测试要点:');
    console.log('   1. 页面加载时第一个公众号应该被选中');
    console.log('   2. 点击不同公众号，文章列表应该切换');
    console.log('   3. 选中的公众号有蓝色左边框和背景色');
    console.log('   4. 文章显示封面图、标题和时间');
    console.log('   5. 每个公众号最多显示10篇文章');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testAccountSelection();
