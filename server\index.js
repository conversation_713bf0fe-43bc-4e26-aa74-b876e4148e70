const express = require('express');
const cors = require('cors');
const cron = require('node-cron');
require('dotenv').config();

const Database = require('./database');
const WeChatService = require('./wechat-service');

const app = express();
const PORT = process.env.PORT || 5000;

// 初始化数据库和微信服务
const db = new Database();
const wechatService = new WeChatService(db);

// 中间件
app.use(cors());
app.use(express.json());

// 路由

// 搜索公众号
app.post('/api/search-account', async (req, res) => {
  try {
    const { name } = req.body;
    if (!name) {
      return res.status(400).json({ error: '公众号名称不能为空' });
    }

    const result = await wechatService.searchAccount(name);
    res.json(result);
  } catch (error) {
    console.error('搜索公众号失败:', error);
    res.status(500).json({ error: '搜索失败，请稍后重试' });
  }
});

// 获取热门公众号
app.get('/api/popular-accounts', async (req, res) => {
  try {
    const popular = wechatService.getPopularAccounts();
    res.json({ success: true, data: popular });
  } catch (error) {
    console.error('获取热门公众号失败:', error);
    res.status(500).json({ error: '获取失败，请稍后重试' });
  }
});

// 获取公众号分类
app.get('/api/categories', async (req, res) => {
  try {
    const { getCategories } = require('./real-accounts-data');
    const categories = getCategories();
    res.json({ success: true, data: categories });
  } catch (error) {
    console.error('获取分类失败:', error);
    res.status(500).json({ error: '获取失败，请稍后重试' });
  }
});

// 按分类获取公众号
app.get('/api/accounts-by-category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const { getAccountsByCategory } = require('./real-accounts-data');
    const accounts = getAccountsByCategory(category);
    res.json({ success: true, data: accounts });
  } catch (error) {
    console.error('按分类获取公众号失败:', error);
    res.status(500).json({ error: '获取失败，请稍后重试' });
  }
});

// 添加公众号到监控列表
app.post('/api/accounts', async (req, res) => {
  try {
    const account = req.body;
    const result = await db.addAccount(account);
    
    // 添加日志
    await db.addLog({
      account_id: result.id,
      action: 'add_account',
      message: `添加公众号: ${account.name}`,
      status: 'success'
    });

    res.json(result);
  } catch (error) {
    console.error('添加公众号失败:', error);
    if (error.message.includes('UNIQUE constraint failed')) {
      res.status(400).json({ error: '该公众号已在监控列表中' });
    } else {
      res.status(500).json({ error: '添加失败，请稍后重试' });
    }
  }
});

// 获取监控列表
app.get('/api/accounts', async (req, res) => {
  try {
    const accounts = await db.getAllAccounts();
    res.json(accounts);
  } catch (error) {
    console.error('获取监控列表失败:', error);
    res.status(500).json({ error: '获取列表失败' });
  }
});

// 删除公众号
app.delete('/api/accounts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await db.deleteAccount(id);
    
    if (result.changes > 0) {
      await db.addLog({
        account_id: parseInt(id),
        action: 'delete_account',
        message: `删除公众号`,
        status: 'success'
      });
      res.json({ message: '删除成功' });
    } else {
      res.status(404).json({ error: '公众号不存在' });
    }
  } catch (error) {
    console.error('删除公众号失败:', error);
    res.status(500).json({ error: '删除失败' });
  }
});

// 获取文章列表
app.get('/api/articles', async (req, res) => {
  try {
    const { account_id, limit = 50 } = req.query;
    const articles = await db.getArticles(account_id, parseInt(limit));
    res.json(articles);
  } catch (error) {
    console.error('获取文章列表失败:', error);
    res.status(500).json({ error: '获取文章列表失败' });
  }
});

// 手动刷新某个公众号的文章
app.post('/api/refresh/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await wechatService.fetchAccountArticles(id);
    res.json(result);
  } catch (error) {
    console.error('刷新文章失败:', error);
    res.status(500).json({ error: '刷新失败' });
  }
});

// 定时任务：每10分钟检查一次所有公众号的新文章
cron.schedule('*/10 * * * *', async () => {
  console.log('开始定时检查公众号更新...');
  try {
    await wechatService.checkAllAccountsForUpdates();
    console.log('定时检查完成');
  } catch (error) {
    console.error('定时检查失败:', error);
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`API地址: http://localhost:${PORT}/api`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  db.close();
  process.exit(0);
});
