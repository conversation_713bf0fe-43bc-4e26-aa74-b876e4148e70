{"name": "rss-parser", "version": "3.13.0", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter-option maxDiffSize=0 --exit", "build": "./scripts/build.sh"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@types/xml2js": "^0.4.3", "babel-core": "^6.26.3", "babel-loader": "^8.0.4", "babel-preset-env": "^1.7.0", "chai": "^3.4.1", "express": "^4.16.3", "mocha": "^10.2.0", "puppeteer": "^5.2.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.9"}, "dependencies": {"entities": "^2.0.3", "xml2js": "^0.5.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/bobby-brennan/rss-parser.git"}, "bugs": {"url": "https://github.com/bobby-brennan/rss-parser/issues"}, "homepage": "https://github.com/bobby-brennan/rss-parser#readme", "description": "A lightweight RSS parser, for Node and the browser", "keywords": ["RSS", "RSS to JSON", "RSS reader", "RSS parser", "RSS to JS", "Feed reader"]}