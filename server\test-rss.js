const RSSService = require('./rss-service');

async function testRSSService() {
  console.log('开始测试RSS服务...');
  
  const rssService = new RSSService();
  
  // 测试1: 获取推荐RSS源
  console.log('\n=== 测试1: 获取推荐RSS源 ===');
  try {
    const recommended = rssService.getRecommendedRSSFeeds();
    console.log('推荐RSS源数量:', recommended.length);
    console.log('第一个推荐源:', recommended[0]);
  } catch (error) {
    console.error('获取推荐RSS源失败:', error);
  }
  
  // 测试2: 验证RSS URL
  console.log('\n=== 测试2: 验证RSS URL ===');
  const testUrls = [
    'http://www.ruanyifeng.com/blog/atom.xml',
    'https://sspai.com/feed',
    'https://invalid-url.com/feed.xml'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`\n验证URL: ${url}`);
      const result = await rssService.validateRSSUrl(url);
      console.log('验证结果:', result);
    } catch (error) {
      console.error(`验证失败 ${url}:`, error.message);
    }
  }
  
  // 测试3: 获取RSS内容
  console.log('\n=== 测试3: 获取RSS内容 ===');
  try {
    const feed = await rssService.fetchRSSFeed('http://www.ruanyifeng.com/blog/atom.xml');
    console.log('RSS标题:', feed.title);
    console.log('文章数量:', feed.items.length);
    if (feed.items.length > 0) {
      console.log('第一篇文章:', {
        title: feed.items[0].title,
        link: feed.items[0].link,
        pubDate: feed.items[0].pubDate
      });
    }
  } catch (error) {
    console.error('获取RSS内容失败:', error.message);
  }
}

// 运行测试
testRSSService().then(() => {
  console.log('\nRSS服务测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试过程中发生错误:', error);
  process.exit(1);
});
