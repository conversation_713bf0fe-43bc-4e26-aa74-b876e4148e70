const axios = require('axios');
const cheerio = require('cheerio');
const { searchAccounts, getCategories, getAccountsByCategory } = require('./real-accounts-data');

class WeChatService {
  constructor(database) {
    this.db = database;
    this.baseUrl = 'https://mp.weixin.qq.com';
  }

  // 搜索公众号（使用真实数据）
  async searchAccount(name) {
    try {
      console.log(`搜索公众号: ${name}`);

      // 使用真实公众号数据进行搜索
      const results = searchAccounts(name);

      if (results.length > 0) {
        // 返回第一个最匹配的结果
        const bestMatch = results[0];
        console.log(`找到匹配结果: ${bestMatch.name}`);

        return {
          success: true,
          data: {
            name: bestMatch.name,
            avatar: bestMatch.avatar,
            description: bestMatch.description,
            biz: bestMatch.biz,
            verified: bestMatch.verified,
            category: bestMatch.category,
            followers: bestMatch.followers,
            location: bestMatch.location
          },
          suggestions: results.slice(1, 6) // 返回其他建议结果
        };
      } else {
        // 如果没有找到匹配结果，返回建议
        console.log(`未找到匹配结果: ${name}`);

        return {
          success: false,
          message: '未找到匹配的公众号',
          suggestions: this.getPopularAccounts() // 返回热门公众号作为建议
        };
      }
    } catch (error) {
      console.error('搜索公众号失败:', error);
      return {
        success: false,
        error: '搜索失败，请检查网络连接'
      };
    }
  }

  // 获取热门公众号
  getPopularAccounts() {
    const categories = getCategories();
    const popular = [];

    // 从每个分类中选择一个热门账号
    categories.forEach(category => {
      const accounts = getAccountsByCategory(category);
      if (accounts.length > 0) {
        popular.push(accounts[0]); // 取每个分类的第一个（最热门的）
      }
    });

    return popular.slice(0, 8); // 返回8个热门账号
  }

  // 生成模拟的biz参数
  generateMockBiz(name) {
    // 简单的字符串哈希生成模拟biz
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      const char = name.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Buffer.from(Math.abs(hash).toString()).toString('base64');
  }

  // 获取公众号文章列表（模拟实现）
  async fetchAccountArticles(accountId) {
    try {
      // 从数据库获取公众号信息
      const accounts = await this.db.getAllAccounts();
      const account = accounts.find(acc => acc.id === parseInt(accountId));
      
      if (!account) {
        throw new Error('公众号不存在');
      }

      // 模拟获取文章列表
      const mockArticles = this.generateMockArticles(account);
      
      // 保存新文章到数据库
      let newArticlesCount = 0;
      for (const article of mockArticles) {
        const exists = await this.db.articleExists(
          account.id, 
          article.title, 
          article.publish_time
        );
        
        if (!exists) {
          await this.db.addArticle({
            account_id: account.id,
            ...article
          });
          newArticlesCount++;
        }
      }

      // 记录日志
      await this.db.addLog({
        account_id: account.id,
        action: 'fetch_articles',
        message: `获取到 ${newArticlesCount} 篇新文章`,
        status: 'success'
      });

      return {
        success: true,
        newArticlesCount,
        totalArticles: mockArticles.length
      };

    } catch (error) {
      console.error('获取文章失败:', error);
      
      if (accountId) {
        await this.db.addLog({
          account_id: parseInt(accountId),
          action: 'fetch_articles',
          message: `获取文章失败: ${error.message}`,
          status: 'error'
        });
      }

      throw error;
    }
  }

  // 生成模拟文章数据
  generateMockArticles(account) {
    const articles = [];
    const now = new Date();
    
    for (let i = 0; i < 10; i++) {
      const publishTime = new Date(now.getTime() - i * 24 * 60 * 60 * 1000); // 每天一篇
      
      articles.push({
        title: `${account.name}最新资讯 ${i + 1} - ${publishTime.toLocaleDateString()}`,
        url: `https://mp.weixin.qq.com/s/${this.generateRandomString(22)}`,
        publish_time: publishTime.toISOString(),
        content_url: `https://mp.weixin.qq.com/s/${this.generateRandomString(22)}`,
        digest: `这是${account.name}发布的第${i + 1}篇文章的摘要内容...`,
        cover_url: `https://picsum.photos/300/200?random=${i + Date.now()}`,
        author: account.name,
        read_count: Math.floor(Math.random() * 10000),
        like_count: Math.floor(Math.random() * 1000)
      });
    }
    
    return articles;
  }

  // 生成随机字符串
  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 检查所有公众号的更新
  async checkAllAccountsForUpdates() {
    try {
      const accounts = await this.db.getAllAccounts();
      console.log(`开始检查 ${accounts.length} 个公众号的更新`);

      for (const account of accounts) {
        try {
          await this.fetchAccountArticles(account.id);
          console.log(`✓ 检查完成: ${account.name}`);
          
          // 避免请求过于频繁
          await this.sleep(2000);
        } catch (error) {
          console.error(`✗ 检查失败: ${account.name}`, error.message);
        }
      }

      console.log('所有公众号检查完成');
    } catch (error) {
      console.error('批量检查失败:', error);
    }
  }

  // 延时函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = WeChatService;
