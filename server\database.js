const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    const dbPath = path.join(__dirname, 'wx_monitor.db');
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('数据库连接失败:', err.message);
      } else {
        console.log('数据库连接成功');
        this.createTables();
      }
    });
  }

  createTables() {
    // 公众号表
    const createAccountsTable = `
      CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        avatar TEXT,
        description TEXT,
        biz TEXT,
        rss_url TEXT,
        source_type TEXT DEFAULT 'wechat',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active INTEGER DEFAULT 1
      )
    `;

    // 文章表
    const createArticlesTable = `
      CREATE TABLE IF NOT EXISTS articles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        url TEXT,
        publish_time DATETIME,
        content_url TEXT,
        digest TEXT,
        cover_url TEXT,
        author TEXT,
        read_count INTEGER DEFAULT 0,
        like_count INTEGER DEFAULT 0,
        category TEXT,
        source_type TEXT DEFAULT 'wechat',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
      )
    `;

    // 监控日志表
    const createLogsTable = `
      CREATE TABLE IF NOT EXISTS monitor_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        action TEXT NOT NULL,
        message TEXT,
        status TEXT DEFAULT 'success',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
      )
    `;

    this.db.run(createAccountsTable, (err) => {
      if (err) console.error('创建accounts表失败:', err.message);
      else console.log('accounts表创建成功');
    });

    this.db.run(createArticlesTable, (err) => {
      if (err) console.error('创建articles表失败:', err.message);
      else console.log('articles表创建成功');
    });

    this.db.run(createLogsTable, (err) => {
      if (err) console.error('创建monitor_logs表失败:', err.message);
      else console.log('monitor_logs表创建成功');
    });
  }

  // 添加公众号
  addAccount(account) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO accounts (name, avatar, description, biz) VALUES (?, ?, ?, ?)`;
      this.db.run(sql, [account.name, account.avatar, account.description, account.biz], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...account });
        }
      });
    });
  }

  // 获取所有公众号
  getAllAccounts() {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM accounts WHERE is_active = 1 ORDER BY created_at DESC`;
      this.db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 删除公众号
  deleteAccount(id) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE accounts SET is_active = 0 WHERE id = ?`;
      this.db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // 添加文章
  addArticle(article) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO articles (account_id, title, url, publish_time, content_url, digest, cover_url, author) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
      this.db.run(sql, [
        article.account_id, article.title, article.url, article.publish_time,
        article.content_url, article.digest, article.cover_url, article.author
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...article });
        }
      });
    });
  }

  // 获取文章列表
  getArticles(accountId = null, limit = 10) {
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT a.*, acc.name as account_name, acc.avatar as account_avatar
        FROM articles a
        LEFT JOIN accounts acc ON a.account_id = acc.id
        WHERE acc.is_active = 1
      `;
      let params = [];

      if (accountId) {
        sql += ` AND a.account_id = ?`;
        params.push(accountId);
      }

      sql += ` ORDER BY a.publish_time DESC LIMIT ?`;
      params.push(limit);

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 检查文章是否已存在
  articleExists(accountId, title, publishTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM articles WHERE account_id = ? AND title = ? AND publish_time = ?`;
      this.db.get(sql, [accountId, title, publishTime], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(!!row);
        }
      });
    });
  }

  // 添加监控日志
  addLog(log) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO monitor_logs (account_id, action, message, status) VALUES (?, ?, ?, ?)`;
      this.db.run(sql, [log.account_id, log.action, log.message, log.status], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...log });
        }
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('关闭数据库失败:', err.message);
        } else {
          console.log('数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = Database;
