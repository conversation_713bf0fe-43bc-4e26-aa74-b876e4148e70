body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

* {
  box-sizing: border-box;
}

.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-content {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 32px;
  padding: 20px 0;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 12px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  font-weight: 600;
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
}

/* 自定义滚动条样式 */
.content-card .ant-card-body {
  padding: 24px;
}

.content-card::-webkit-scrollbar {
  width: 6px;
}

.content-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 列表项悬停效果 */
.ant-list-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px !important;
}

.ant-list-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 优化列表项内容布局 */
.ant-list-item-meta {
  align-items: flex-start !important;
}

.ant-list-item-meta-content {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.ant-list-item-meta-title {
  margin-bottom: 8px !important;
  line-height: 1.4 !important;
}

.ant-list-item-meta-description {
  margin-top: 0 !important;
}

/* 文章标题样式 */
.article-title-link {
  color: #333 !important;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  display: block;
  margin-bottom: 8px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.article-title-link:hover {
  color: #1890ff !important;
}

/* 徽章样式 */
.ant-badge .ant-badge-count {
  background-color: #52c41a;
  border-color: #52c41a;
}

/* 搜索结果卡片样式 */
.search-result-card {
  border: 1px solid #1890ff !important;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%) !important;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state .anticon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
}

/* 响应式调整 */
@media (max-width: 1600px) {
  .main-content {
    max-width: 100%;
    padding: 20px;
  }
}

@media (max-width: 1200px) {
  .main-content {
    padding: 16px;
  }

  .content-card {
    height: 500px !important;
  }
}

@media (max-width: 992px) {
  .main-content {
    padding: 12px;
  }

  .content-card {
    height: 450px !important;
    margin-bottom: 16px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .header p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .content-card {
    height: auto !important;
    max-height: 400px;
    margin-bottom: 16px;
  }

  .ant-col {
    margin-bottom: 16px;
  }

  /* 移动端文章列表优化 */
  .ant-list-item-meta-title {
    margin-bottom: 8px !important;
  }

  .ant-list-item-meta-description {
    margin-top: 4px !important;
  }
}

@media (max-width: 576px) {
  .header h1 {
    font-size: 1.8rem;
  }

  .content-card .ant-card-body {
    padding: 16px;
  }

  .content-card {
    max-height: 350px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .content-card {
    margin-bottom: 15px;
  }
  
  .search-section,
  .accounts-section,
  .articles-section {
    padding: 16px;
  }
}
