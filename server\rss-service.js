const Parser = require('rss-parser');
const axios = require('axios');

class RSSService {
  constructor(database) {
    this.db = database;
    this.parser = new Parser({
      customFields: {
        feed: ['language', 'copyright', 'managingEditor'],
        item: ['author', 'comments', 'source', 'enclosure', 'category']
      }
    });
  }

  // 获取RSS源的文章
  async fetchRSSFeed(rssUrl, accountId) {
    try {
      console.log(`开始获取RSS源: ${rssUrl}`);
      
      // 设置请求头，模拟浏览器访问
      const response = await axios.get(rssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/rss+xml, application/xml, text/xml, */*',
          'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8'
        },
        timeout: 10000
      });

      // 解析RSS内容
      const feed = await this.parser.parseString(response.data);
      console.log(`RSS解析成功: ${feed.title}, 文章数量: ${feed.items.length}`);

      // 处理文章数据
      const articles = [];
      let newArticlesCount = 0;

      for (const item of feed.items) {
        try {
          // 构建文章对象
          const article = {
            title: item.title || '无标题',
            url: item.link || item.guid || '',
            publish_time: this.parseDate(item.pubDate || item.isoDate),
            content_url: item.link || item.guid || '',
            digest: this.extractDigest(item.contentSnippet || item.content || item.description || ''),
            cover_url: this.extractCoverImage(item),
            author: item.author || item.creator || feed.title || '未知作者',
            read_count: 0,
            like_count: 0,
            category: item.category || '',
            source_type: 'rss'
          };

          // 检查文章是否已存在
          const exists = await this.db.articleExists(
            accountId,
            article.title,
            article.publish_time
          );

          if (!exists) {
            await this.db.addArticle({
              account_id: accountId,
              ...article
            });
            newArticlesCount++;
            console.log(`新增文章: ${article.title}`);
          }

          articles.push(article);
        } catch (error) {
          console.error('处理单篇文章失败:', error.message);
        }
      }

      // 记录日志
      await this.db.addLog({
        account_id: accountId,
        action: 'fetch_rss',
        message: `RSS获取成功: ${newArticlesCount} 篇新文章`,
        status: 'success'
      });

      return {
        success: true,
        feedTitle: feed.title,
        feedDescription: feed.description,
        newArticlesCount,
        totalArticles: articles.length,
        articles: articles.slice(0, 10) // 返回最新10篇
      };

    } catch (error) {
      console.error('RSS获取失败:', error);
      
      // 记录错误日志
      await this.db.addLog({
        account_id: accountId,
        action: 'fetch_rss',
        message: `RSS获取失败: ${error.message}`,
        status: 'error'
      });

      throw new Error(`RSS获取失败: ${error.message}`);
    }
  }

  // 解析日期
  parseDate(dateString) {
    if (!dateString) return new Date().toISOString();
    
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date().toISOString() : date.toISOString();
    } catch (error) {
      return new Date().toISOString();
    }
  }

  // 提取摘要
  extractDigest(content) {
    if (!content) return '';
    
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // 截取前200个字符作为摘要
    return textContent.length > 200 
      ? textContent.substring(0, 200) + '...'
      : textContent;
  }

  // 提取封面图片
  extractCoverImage(item) {
    // 1. 检查enclosure中的图片
    if (item.enclosure && item.enclosure.type && item.enclosure.type.startsWith('image/')) {
      return item.enclosure.url;
    }

    // 2. 检查content中的图片
    if (item.content) {
      const imgMatch = item.content.match(/<img[^>]+src="([^"]+)"/i);
      if (imgMatch) {
        return imgMatch[1];
      }
    }

    // 3. 检查description中的图片
    if (item.description) {
      const imgMatch = item.description.match(/<img[^>]+src="([^"]+)"/i);
      if (imgMatch) {
        return imgMatch[1];
      }
    }

    // 4. 检查media:content或media:thumbnail
    if (item['media:content']) {
      return item['media:content'].$.url;
    }

    if (item['media:thumbnail']) {
      return item['media:thumbnail'].$.url;
    }

    // 5. 默认使用随机图片
    return `https://picsum.photos/300/200?random=${Date.now()}`;
  }

  // 验证RSS URL是否有效
  async validateRSSUrl(url) {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/rss+xml, application/xml, text/xml, */*'
        },
        timeout: 5000
      });

      // 尝试解析RSS
      const feed = await this.parser.parseString(response.data);
      
      return {
        valid: true,
        title: feed.title,
        description: feed.description,
        itemCount: feed.items.length,
        lastUpdated: feed.lastBuildDate || feed.pubDate
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // 获取常用RSS源推荐
  getRecommendedRSSFeeds() {
    return [
      {
        name: '人民日报',
        category: '新闻媒体',
        rssUrl: 'http://www.people.com.cn/rss/politics.xml',
        description: '人民日报政治新闻RSS'
      },
      {
        name: '新华网',
        category: '新闻媒体', 
        rssUrl: 'http://www.xinhuanet.com/politics/news_politics.xml',
        description: '新华网政治新闻RSS'
      },
      {
        name: '36氪',
        category: '科技',
        rssUrl: 'https://36kr.com/feed',
        description: '36氪科技资讯RSS'
      },
      {
        name: '少数派',
        category: '科技',
        rssUrl: 'https://sspai.com/feed',
        description: '少数派科技文章RSS'
      },
      {
        name: '阮一峰的网络日志',
        category: '技术博客',
        rssUrl: 'http://www.ruanyifeng.com/blog/atom.xml',
        description: '阮一峰技术博客RSS'
      },
      {
        name: 'InfoQ中文',
        category: '技术',
        rssUrl: 'https://www.infoq.cn/feed',
        description: 'InfoQ技术资讯RSS'
      },
      {
        name: 'V2EX',
        category: '技术社区',
        rssUrl: 'https://www.v2ex.com/index.xml',
        description: 'V2EX技术讨论RSS'
      },
      {
        name: 'Hacker News',
        category: '技术资讯',
        rssUrl: 'https://hnrss.org/frontpage',
        description: 'Hacker News前端页面RSS'
      }
    ];
  }

  // 自动发现网站的RSS源
  async discoverRSSFeeds(websiteUrl) {
    try {
      const response = await axios.get(websiteUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 5000
      });

      const html = response.data;
      const feeds = [];

      // 查找RSS链接
      const rssPatterns = [
        /<link[^>]+type="application\/rss\+xml"[^>]*href="([^"]+)"/gi,
        /<link[^>]+href="([^"]+)"[^>]*type="application\/rss\+xml"/gi,
        /<link[^>]+type="application\/atom\+xml"[^>]*href="([^"]+)"/gi,
        /<link[^>]+href="([^"]+)"[^>]*type="application\/atom\+xml"/gi
      ];

      for (const pattern of rssPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let feedUrl = match[1];
          
          // 处理相对URL
          if (feedUrl.startsWith('/')) {
            const baseUrl = new URL(websiteUrl);
            feedUrl = `${baseUrl.protocol}//${baseUrl.host}${feedUrl}`;
          } else if (!feedUrl.startsWith('http')) {
            feedUrl = `${websiteUrl}/${feedUrl}`;
          }

          feeds.push(feedUrl);
        }
      }

      // 去重
      return [...new Set(feeds)];
    } catch (error) {
      console.error('RSS发现失败:', error);
      return [];
    }
  }
}

module.exports = RSSService;
