// PC桌面端布局测试脚本
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testDesktopLayout() {
  console.log('🖥️  开始PC桌面端布局测试...\n');

  try {
    // 添加一些具有长标题和长描述的测试数据
    const testAccounts = [
      {
        name: '中国新闻网',
        avatar: 'https://via.placeholder.com/100x100?text=中国新闻网',
        description: '中国新闻网官方微信公众号，提供全面、及时、权威的新闻资讯服务，涵盖国内外重大新闻事件',
        biz: 'MjM5MjQ2MjU2M1='
      },
      {
        name: '科技日报',
        avatar: 'https://via.placeholder.com/100x100?text=科技日报',
        description: '科技日报官方微信公众号，专注科技创新报道，传播前沿科技资讯和科学知识',
        biz: 'MjM5MjQ2MjU2M2='
      }
    ];

    console.log('1. 添加长标题测试数据...');
    for (const account of testAccounts) {
      try {
        const result = await axios.post('/api/accounts', account);
        console.log(`✅ 添加成功: ${result.data.name}`);
        
        // 生成一些长标题的测试文章
        console.log(`   正在为 ${result.data.name} 生成长标题测试文章...`);
        const refreshResult = await axios.post(`/api/refresh/${result.data.id}`);
        console.log(`   ✅ 生成了 ${refreshResult.data.newArticlesCount} 篇文章`);
        
      } catch (error) {
        if (error.response?.data?.error?.includes('已在监控列表中')) {
          console.log(`ℹ️  ${account.name} 已存在，跳过添加`);
        } else {
          console.error(`❌ 添加 ${account.name} 失败:`, error.message);
        }
      }
    }

    // 检查当前状态
    console.log('\n2. 检查当前数据状态...');
    const accounts = await axios.get('/api/accounts');
    const articles = await axios.get('/api/articles?limit=15');
    
    console.log(`✅ 当前监控 ${accounts.data.length} 个公众号:`);
    accounts.data.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.name}`);
      console.log(`      描述: ${account.description.substring(0, 50)}${account.description.length > 50 ? '...' : ''}`);
    });
    
    console.log(`\n✅ 当前有 ${articles.data.length} 篇文章，标题示例:`);
    articles.data.slice(0, 5).forEach((article, index) => {
      console.log(`   ${index + 1}. ${article.title.substring(0, 60)}${article.title.length > 60 ? '...' : ''}`);
      console.log(`      来源: ${article.account_name}`);
    });

    console.log('\n🎯 PC桌面端布局测试要点:');
    console.log('   ✓ 监控列表宽度: 42% (xxl: 10/24, xl: 11/24, lg: 12/24)');
    console.log('   ✓ 文章列表宽度: 58% (xxl: 14/24, xl: 13/24, lg: 12/24)');
    console.log('   ✓ 最大容器宽度: 1800px');
    console.log('   ✓ 卡片高度: 700px，支持滚动');
    console.log('   ✓ 文字换行: 使用 word-break: break-word');
    console.log('   ✓ 自定义布局: 不依赖 Ant Design 默认 List.Item.Meta');

    console.log('\n📱 请在浏览器中访问 http://localhost:3000');
    console.log('💡 建议在1920px或更大的屏幕上测试效果');
    console.log('🔍 检查要点:');
    console.log('   - 监控列表中的公众号名称和描述是否正常显示');
    console.log('   - 文章列表中的标题是否完整显示，无异常换行');
    console.log('   - 操作按钮是否正常对齐');
    console.log('   - 整体布局是否协调美观');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testDesktopLayout();
