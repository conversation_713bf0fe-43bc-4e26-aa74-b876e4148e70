// 简单的API测试脚本
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testAPI() {
  console.log('🚀 开始测试微信公众号监控系统API...\n');

  try {
    // 1. 测试搜索功能
    console.log('1. 测试搜索功能...');
    const searchResult = await axios.post('/api/search-account', { name: '新华社' });
    console.log('✅ 搜索成功:', searchResult.data.data.name);

    // 2. 测试获取监控列表
    console.log('\n2. 测试获取监控列表...');
    const accountsResult = await axios.get('/api/accounts');
    console.log(`✅ 当前监控 ${accountsResult.data.length} 个公众号`);
    accountsResult.data.forEach(account => {
      console.log(`   - ${account.name}`);
    });

    // 3. 测试获取文章列表
    console.log('\n3. 测试获取文章列表...');
    const articlesResult = await axios.get('/api/articles?limit=5');
    console.log(`✅ 获取到 ${articlesResult.data.length} 篇文章`);
    articlesResult.data.forEach(article => {
      console.log(`   - ${article.title} (${article.account_name})`);
    });

    // 4. 测试添加新公众号
    console.log('\n4. 测试添加新公众号...');
    try {
      const addResult = await axios.post('/api/accounts', {
        name: '新华社',
        avatar: 'https://via.placeholder.com/100x100?text=新华社',
        description: '新华社官方微信公众号',
        biz: 'MjM5MjQ2MjU2MQ=='
      });
      console.log('✅ 添加成功:', addResult.data.name);

      // 5. 测试刷新新添加的公众号
      console.log('\n5. 测试刷新公众号...');
      const refreshResult = await axios.post(`/api/refresh/${addResult.data.id}`);
      console.log(`✅ 刷新成功，获取到 ${refreshResult.data.newArticlesCount} 篇新文章`);

    } catch (error) {
      if (error.response?.data?.error?.includes('已在监控列表中')) {
        console.log('ℹ️  新华社已在监控列表中，跳过添加');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 所有API测试通过！');
    console.log('\n📱 请在浏览器中访问 http://localhost:3000 查看前端界面');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testAPI();
