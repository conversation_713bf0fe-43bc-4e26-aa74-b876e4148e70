@echo off
echo ========================================
echo 微信公众号监控系统启动脚本
echo ========================================
echo.

echo 正在检查依赖...
if not exist "node_modules" (
    echo 安装根目录依赖...
    call npm install
)

if not exist "server\node_modules" (
    echo 安装服务端依赖...
    cd server
    call npm install
    cd ..
)

if not exist "client\node_modules" (
    echo 安装客户端依赖...
    cd client
    call npm install
    cd ..
)

echo.
echo 启动系统...
echo 后端服务器: http://localhost:5000
echo 前端界面: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务
echo.

start "微信公众号监控系统" cmd /k "npm run dev"

echo 系统正在启动，请稍等...
timeout /t 5 /nobreak > nul

echo.
echo 启动完成！请在浏览器中访问: http://localhost:3000
echo.
pause
