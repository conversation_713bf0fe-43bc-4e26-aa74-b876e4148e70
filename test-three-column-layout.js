// 三列式布局测试脚本
const axios = require('axios');

const baseURL = 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

async function testThreeColumnLayout() {
  console.log('🏛️  开始三列式布局测试...\n');

  try {
    // 清理现有数据，重新开始测试
    console.log('1. 准备测试环境...');
    
    // 获取现有账号并删除（可选）
    const existingAccounts = await axios.get('/api/accounts');
    console.log(`   当前有 ${existingAccounts.data.length} 个监控账号`);

    // 添加适合三列布局的测试数据
    const testAccounts = [
      {
        name: '人民日报',
        avatar: 'https://via.placeholder.com/100x100?text=人民日报',
        description: '人民日报官方微信公众号，传播权威资讯',
        biz: 'MjM5MjQ2MjU2MA=='
      },
      {
        name: '新华社',
        avatar: 'https://via.placeholder.com/100x100?text=新华社',
        description: '新华社官方微信公众号，国家通讯社',
        biz: 'MjM5MjQ2MjU2MQ=='
      },
      {
        name: '央视新闻',
        avatar: 'https://via.placeholder.com/100x100?text=央视新闻',
        description: '央视新闻官方微信公众号，权威新闻资讯',
        biz: 'MjM5MjQ2MjU2Mg=='
      },
      {
        name: '澎湃新闻',
        avatar: 'https://via.placeholder.com/100x100?text=澎湃新闻',
        description: '澎湃新闻官方微信公众号，专业新闻报道',
        biz: 'MjM5MjQ2MjU2Mw=='
      },
      {
        name: '科技日报',
        avatar: 'https://via.placeholder.com/100x100?text=科技日报',
        description: '科技日报官方微信公众号，专注科技创新报道',
        biz: 'MjM5MjQ2MjU2M3='
      }
    ];

    console.log('\n2. 添加测试公众号...');
    let addedCount = 0;
    for (const account of testAccounts) {
      try {
        const result = await axios.post('/api/accounts', account);
        console.log(`✅ 添加成功: ${result.data.name}`);
        addedCount++;
        
        // 为每个公众号生成文章
        const refreshResult = await axios.post(`/api/refresh/${result.data.id}`);
        console.log(`   ✅ 生成了 ${refreshResult.data.newArticlesCount} 篇文章`);
        
      } catch (error) {
        if (error.response?.data?.error?.includes('已在监控列表中')) {
          console.log(`ℹ️  ${account.name} 已存在，刷新文章...`);
          
          // 获取现有账号ID并刷新
          const accountsResult = await axios.get('/api/accounts');
          const existingAccount = accountsResult.data.find(acc => acc.name === account.name);
          if (existingAccount) {
            const refreshResult = await axios.post(`/api/refresh/${existingAccount.id}`);
            console.log(`   ✅ 刷新了 ${existingAccount.name}，生成 ${refreshResult.data.newArticlesCount} 篇新文章`);
          }
        } else {
          console.error(`❌ 添加 ${account.name} 失败:`, error.message);
        }
      }
    }

    // 检查最终状态
    console.log('\n3. 检查三列式布局数据状态...');
    const finalAccounts = await axios.get('/api/accounts');
    const finalArticles = await axios.get('/api/articles?limit=20');
    
    console.log(`✅ 第二列（监控列表）: ${finalAccounts.data.length} 个公众号`);
    finalAccounts.data.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.name}`);
    });
    
    console.log(`\n✅ 第三列（文章列表）: ${finalArticles.data.length} 篇文章`);
    finalArticles.data.slice(0, 8).forEach((article, index) => {
      console.log(`   ${index + 1}. ${article.title.substring(0, 40)}... - ${article.account_name}`);
    });

    console.log('\n🏛️  三列式布局设计说明:');
    console.log('   📱 第一列（添加公众号）: 29% 宽度 (7/24)');
    console.log('      - 搜索框和搜索结果');
    console.log('      - 紧凑的垂直布局');
    console.log('      - 操作提示信息');
    
    console.log('   📋 第二列（监控列表）: 33% 宽度 (8/24)');
    console.log('      - 已添加的公众号列表');
    console.log('      - 紧凑的列表项设计');
    console.log('      - 刷新和删除操作');
    
    console.log('   📰 第三列（文章列表）: 38% 宽度 (9/24)');
    console.log('      - 最新文章展示');
    console.log('      - 文章标题和摘要');
    console.log('      - 发布时间和来源');

    console.log('\n🎯 三列式布局优势:');
    console.log('   ✓ 功能区域明确分离');
    console.log('   ✓ 操作流程更加直观');
    console.log('   ✓ 信息密度更高');
    console.log('   ✓ 专为PC大屏优化');
    console.log('   ✓ 减少了添加公众号区域的高度');

    console.log('\n📱 请在浏览器中访问 http://localhost:3000');
    console.log('💡 建议在1920px宽屏上测试效果');
    console.log('🔍 检查要点:');
    console.log('   - 第一列：添加公众号功能是否紧凑');
    console.log('   - 第二列：监控列表是否显示完整');
    console.log('   - 第三列：文章列表是否排版美观');
    console.log('   - 整体：三列是否协调统一');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testThreeColumnLayout();
