import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Input,
  Button,
  List,
  Avatar,
  message,
  Space,
  Typography,
  Divider,
  Empty,
  Spin,
  Tag,
  Popconfirm,
  Row,
  Col,
  Badge
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  WechatOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  EyeOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:5000';

function App() {
  const [searchValue, setSearchValue] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [searching, setSearching] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [articlesLoading, setArticlesLoading] = useState(false);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAccounts();
    fetchArticles();
  }, []);

  // 搜索公众号
  const handleSearch = async (value) => {
    if (!value.trim()) {
      message.warning('请输入公众号名称');
      return;
    }

    setSearching(true);
    try {
      const response = await axios.post('/api/search-account', { name: value.trim() });
      if (response.data.success) {
        setSearchResult(response.data.data);
        message.success('搜索成功');
      } else {
        message.error(response.data.error || '搜索失败');
        setSearchResult(null);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败，请检查网络连接');
      setSearchResult(null);
    } finally {
      setSearching(false);
    }
  };

  // 添加公众号到监控列表
  const handleAddAccount = async () => {
    if (!searchResult) {
      message.warning('请先搜索公众号');
      return;
    }

    try {
      await axios.post('/api/accounts', searchResult);
      message.success('添加成功');
      setSearchResult(null);
      setSearchValue('');
      fetchAccounts();
      fetchArticles(); // 刷新文章列表
    } catch (error) {
      console.error('添加失败:', error);
      if (error.response?.data?.error) {
        message.error(error.response.data.error);
      } else {
        message.error('添加失败，请稍后重试');
      }
    }
  };

  // 获取监控列表
  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/accounts');
      setAccounts(response.data);
    } catch (error) {
      console.error('获取监控列表失败:', error);
      message.error('获取监控列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除公众号
  const handleDeleteAccount = async (id) => {
    try {
      await axios.delete(`/api/accounts/${id}`);
      message.success('删除成功');
      fetchAccounts();
      fetchArticles(); // 刷新文章列表
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 获取文章列表
  const fetchArticles = async () => {
    setArticlesLoading(true);
    try {
      const response = await axios.get('/api/articles?limit=50');
      setArticles(response.data);
    } catch (error) {
      console.error('获取文章列表失败:', error);
      message.error('获取文章列表失败');
    } finally {
      setArticlesLoading(false);
    }
  };

  // 手动刷新某个公众号
  const handleRefreshAccount = async (id) => {
    try {
      message.loading('正在刷新...', 0);
      const response = await axios.post(`/api/refresh/${id}`);
      message.destroy();
      
      if (response.data.success) {
        message.success(`刷新成功，获取到 ${response.data.newArticlesCount} 篇新文章`);
        fetchArticles();
      } else {
        message.error('刷新失败');
      }
    } catch (error) {
      message.destroy();
      console.error('刷新失败:', error);
      message.error('刷新失败');
    }
  };

  // 格式化时间
  const formatTime = (timeStr) => {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / ********);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  };

  return (
    <div className="app-container">
      <Layout style={{ minHeight: '100vh', background: 'transparent' }}>
        <Header style={{ background: 'transparent', padding: 0 }}>
          <div className="header">
            <Title level={1} style={{ color: 'white', margin: 0 }}>
              <WechatOutlined /> 微信公众号监控系统
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
              实时监控公众号发文动态，第一时间获取最新资讯
            </Text>
          </div>
        </Header>

        <Content className="main-content">
          {/* 搜索区域 */}
          <Card className="content-card" style={{ marginBottom: 24 }}>
            <Title level={3} style={{ marginBottom: 16 }}>
              <SearchOutlined /> 添加公众号
            </Title>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Search
                  placeholder="请输入公众号名称（如：人民日报、新华社、央视新闻）"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onSearch={handleSearch}
                  loading={searching}
                  size="large"
                  enterButton="搜索"
                />
              </Col>
            </Row>

            {searchResult && (
              <Card
                size="small"
                style={{
                  marginTop: 16,
                  border: '1px solid #1890ff',
                  backgroundColor: '#f6ffed'
                }}
              >
                <Row align="middle" justify="space-between">
                  <Col>
                    <Space size={16}>
                      <Avatar src={searchResult.avatar} size={64} icon={<UserOutlined />} />
                      <div>
                        <Title level={4} style={{ margin: 0, marginBottom: 4 }}>
                          {searchResult.name}
                          {searchResult.verified && <Tag color="blue" style={{ marginLeft: 8 }}>已认证</Tag>}
                        </Title>
                        <Text type="secondary">{searchResult.description}</Text>
                      </div>
                    </Space>
                  </Col>
                  <Col>
                    <Button
                      type="primary"
                      size="large"
                      icon={<PlusOutlined />}
                      onClick={handleAddAccount}
                    >
                      添加到监控
                    </Button>
                  </Col>
                </Row>
              </Card>
            )}
          </Card>

          <Row gutter={[24, 24]}>
            {/* 监控列表 */}
            <Col xs={24} lg={10}>
              <Card className="content-card" style={{ height: '600px', overflow: 'auto' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <Title level={3} style={{ margin: 0 }}>
                    <EyeOutlined /> 监控列表
                    <Badge count={accounts.length} style={{ marginLeft: 8 }} />
                  </Title>
                  <Button icon={<ReloadOutlined />} onClick={fetchAccounts}>
                    刷新
                  </Button>
                </div>

                <Spin spinning={loading}>
                  {accounts.length === 0 ? (
                    <Empty
                      description="暂无监控的公众号"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ) : (
                    <List
                      dataSource={accounts}
                      renderItem={(account) => (
                        <List.Item
                          style={{
                            padding: '12px 0',
                            borderBottom: '1px solid #f0f0f0'
                          }}
                        >
                          <List.Item.Meta
                            avatar={<Avatar src={account.avatar} size={48} icon={<UserOutlined />} />}
                            title={
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <span>{account.name}</span>
                                <Space>
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<ReloadOutlined />}
                                    onClick={() => handleRefreshAccount(account.id)}
                                    title="刷新"
                                  />
                                  <Popconfirm
                                    title="确定要删除这个公众号吗？"
                                    onConfirm={() => handleDeleteAccount(account.id)}
                                    okText="确定"
                                    cancelText="取消"
                                  >
                                    <Button
                                      type="text"
                                      size="small"
                                      danger
                                      icon={<DeleteOutlined />}
                                      title="删除"
                                    />
                                  </Popconfirm>
                                </Space>
                              </div>
                            }
                            description={
                              <div>
                                <div style={{ marginBottom: 4 }}>{account.description}</div>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  添加时间: {new Date(account.created_at).toLocaleString()}
                                </Text>
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  )}
                </Spin>
              </Card>
            </Col>

            {/* 文章列表 */}
            <Col xs={24} lg={14}>
              <Card className="content-card" style={{ height: '600px', overflow: 'auto' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <Title level={3} style={{ margin: 0 }}>
                    <FileTextOutlined /> 最新文章
                    <Badge count={articles.length} style={{ marginLeft: 8 }} />
                  </Title>
                  <Button icon={<ReloadOutlined />} onClick={fetchArticles}>
                    刷新
                  </Button>
                </div>

                <Spin spinning={articlesLoading}>
                  {articles.length === 0 ? (
                    <Empty
                      description="暂无文章数据，请先添加公众号到监控列表"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ) : (
                    <List
                      dataSource={articles}
                      renderItem={(article) => (
                        <List.Item style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
                          <List.Item.Meta
                            avatar={
                              <Avatar
                                src={article.account_avatar}
                                size={40}
                                icon={<UserOutlined />}
                              />
                            }
                            title={
                              <div>
                                <a
                                  href={article.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  style={{
                                    color: '#333',
                                    fontSize: '16px',
                                    fontWeight: 500,
                                    lineHeight: '1.4',
                                    display: 'block',
                                    marginBottom: '8px'
                                  }}
                                >
                                  {article.title}
                                </a>
                                <Space size={16}>
                                  <Text type="secondary" style={{ fontSize: '13px' }}>
                                    {article.account_name}
                                  </Text>
                                  <Text type="secondary" style={{ fontSize: '13px' }}>
                                    <ClockCircleOutlined style={{ marginRight: 4 }} />
                                    {formatTime(article.publish_time)}
                                  </Text>
                                </Space>
                              </div>
                            }
                            description={
                              <Text type="secondary" style={{ fontSize: '14px', lineHeight: '1.5' }}>
                                {article.digest}
                              </Text>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  )}
                </Spin>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </div>
  );
}

export default App;
