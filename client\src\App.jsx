import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Input,
  Button,
  List,
  Avatar,
  message,
  Space,
  Typography,
  Divider,
  Empty,
  Spin,
  Tag,
  Popconfirm,
  Row,
  Col,
  Badge,
  Modal,
  Form,
  Select,
  Tabs
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  WechatOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  EyeOutlined,
  RssOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:5000';

function App() {
  const [searchValue, setSearchValue] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [searching, setSearching] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [selectedAccountId, setSelectedAccountId] = useState(null);

  // RSS相关状态
  const [rssModalVisible, setRssModalVisible] = useState(false);
  const [rssForm] = Form.useForm();
  const [rssValidating, setRssValidating] = useState(false);
  const [recommendedFeeds, setRecommendedFeeds] = useState([]);
  const [rssValidationResult, setRssValidationResult] = useState(null);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAccounts();
  }, []);

  // 监听选中公众号变化，刷新文章列表
  useEffect(() => {
    if (selectedAccountId) {
      fetchArticles();
    }
  }, [selectedAccountId]);

  // 搜索公众号
  const handleSearch = async (value) => {
    if (!value.trim()) {
      message.warning('请输入公众号名称');
      return;
    }

    setSearching(true);
    try {
      const response = await axios.post('/api/search-account', { name: value.trim() });
      if (response.data.success) {
        setSearchResult(response.data.data);
        message.success(`找到公众号: ${response.data.data.name}`);
      } else {
        message.warning(response.data.message || '未找到匹配的公众号');
        setSearchResult(null);
        // 如果有建议，可以显示建议
        if (response.data.suggestions && response.data.suggestions.length > 0) {
          message.info(`建议搜索: ${response.data.suggestions.slice(0, 3).map(s => s.name).join(', ')}`);
        }
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败，请检查网络连接');
      setSearchResult(null);
    } finally {
      setSearching(false);
    }
  };

  // 添加公众号到监控列表
  const handleAddAccount = async () => {
    if (!searchResult) {
      message.warning('请先搜索公众号');
      return;
    }

    try {
      await axios.post('/api/accounts', searchResult);
      message.success('添加成功');
      setSearchResult(null);
      setSearchValue('');
      fetchAccounts();
      fetchArticles(); // 刷新文章列表
    } catch (error) {
      console.error('添加失败:', error);
      if (error.response?.data?.error) {
        message.error(error.response.data.error);
      } else {
        message.error('添加失败，请稍后重试');
      }
    }
  };

  // 获取监控列表
  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/accounts');
      setAccounts(response.data);

      // 如果没有选中的公众号，默认选中第一个
      if (response.data.length > 0 && !selectedAccountId) {
        setSelectedAccountId(response.data[0].id);
      }
    } catch (error) {
      console.error('获取监控列表失败:', error);
      message.error('获取监控列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除公众号
  const handleDeleteAccount = async (id) => {
    try {
      await axios.delete(`/api/accounts/${id}`);
      message.success('删除成功');

      // 如果删除的是当前选中的公众号，重置选中状态
      if (selectedAccountId === id) {
        setSelectedAccountId(null);
      }

      fetchAccounts();
      fetchArticles(); // 刷新文章列表
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 获取文章列表
  const fetchArticles = async (accountId = null) => {
    setArticlesLoading(true);
    try {
      const targetAccountId = accountId || selectedAccountId;
      let url = '/api/articles?limit=10';

      // 如果有选中的公众号，按公众号筛选
      if (targetAccountId) {
        url += `&account_id=${targetAccountId}`;
      }

      const response = await axios.get(url);
      setArticles(response.data);
    } catch (error) {
      console.error('获取文章列表失败:', error);
      message.error('获取文章列表失败');
    } finally {
      setArticlesLoading(false);
    }
  };

  // 手动刷新某个公众号
  const handleRefreshAccount = async (id) => {
    try {
      message.loading('正在刷新...', 0);
      const response = await axios.post(`/api/refresh/${id}`);
      message.destroy();

      if (response.data.success) {
        message.success(`刷新成功，获取到 ${response.data.newArticlesCount} 篇新文章`);
        // 如果刷新的是当前选中的公众号，刷新文章列表
        if (id === selectedAccountId) {
          fetchArticles();
        }
      } else {
        message.error('刷新失败');
      }
    } catch (error) {
      message.destroy();
      console.error('刷新失败:', error);
      message.error('刷新失败');
    }
  };

  // 选择公众号
  const handleSelectAccount = (accountId) => {
    setSelectedAccountId(accountId);
  };

  // RSS相关功能

  // 获取推荐RSS源
  const fetchRecommendedFeeds = async () => {
    try {
      const response = await axios.get('/api/rss/recommended');
      setRecommendedFeeds(response.data.data);
    } catch (error) {
      console.error('获取推荐RSS失败:', error);
    }
  };

  // 验证RSS URL
  const validateRSSUrl = async (url) => {
    if (!url) {
      setRssValidationResult(null);
      return;
    }

    setRssValidating(true);
    try {
      const response = await axios.post('/api/rss/validate', { url });
      setRssValidationResult(response.data);

      if (response.data.valid) {
        // 自动填充名称和描述
        rssForm.setFieldsValue({
          name: response.data.title || '',
          description: response.data.description || ''
        });
      }
    } catch (error) {
      console.error('RSS验证失败:', error);
      setRssValidationResult({ valid: false, error: '验证失败' });
    } finally {
      setRssValidating(false);
    }
  };

  // 添加RSS订阅
  const handleAddRSSSubscription = async (values) => {
    try {
      const response = await axios.post('/api/rss/subscribe', values);
      message.success('RSS订阅添加成功');
      setRssModalVisible(false);
      rssForm.resetFields();
      setRssValidationResult(null);
      fetchAccounts(); // 刷新账号列表
    } catch (error) {
      console.error('添加RSS订阅失败:', error);
      message.error(error.response?.data?.error || '添加RSS订阅失败');
    }
  };

  // 打开RSS订阅弹窗
  const handleOpenRSSModal = () => {
    setRssModalVisible(true);
    fetchRecommendedFeeds();
  };

  // 使用推荐RSS源
  const handleUseRecommendedFeed = (feed) => {
    rssForm.setFieldsValue({
      name: feed.name,
      rssUrl: feed.rssUrl,
      description: feed.description,
      category: feed.category
    });
    validateRSSUrl(feed.rssUrl);
  };

  // 格式化时间
  const formatTime = (timeStr) => {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    const days = Math.floor(diff / ********);

    // 如果是今天，显示具体时分秒
    if (days === 0) {
      return time.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }

    // 如果是昨天，显示"昨天 HH:mm:ss"
    if (days === 1) {
      return '昨天 ' + time.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }

    // 如果是更早，显示完整日期时间
    return time.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  return (
    <div className="app-container">
      <Layout style={{ minHeight: '100vh', background: 'transparent' }}>
        <Header style={{ background: 'transparent', padding: 0 }}>
          <div className="header">
            <Title level={1} style={{ color: 'white', margin: 0 }}>
              <WechatOutlined /> 微信公众号监控系统
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
              实时监控公众号发文动态，第一时间获取最新资讯
            </Text>
          </div>
        </Header>

        <Content className="main-content">
          {/* 三列式布局 */}
          <Row gutter={[24, 24]} style={{ height: '700px' }}>
            {/* 第一列：添加公众号 */}
            <Col span={7}>
              <Card className="content-card" style={{ height: '100%', overflow: 'auto' }}>
                <Title level={3} style={{ marginBottom: 20, fontSize: '18px' }}>
                  <SearchOutlined /> 添加公众号
                </Title>

                <div style={{ marginBottom: 20 }}>
                  <Search
                    placeholder="输入公众号名称"
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onSearch={handleSearch}
                    loading={searching}
                    enterButton="搜索"
                    style={{ marginBottom: 16 }}
                  />

                  <Button
                    type="dashed"
                    block
                    icon={<RssOutlined />}
                    onClick={handleOpenRSSModal}
                    style={{ marginBottom: 16 }}
                  >
                    添加RSS订阅
                  </Button>
                </div>

                {searchResult && (
                  <Card
                    size="small"
                    style={{
                      border: '1px solid #1890ff',
                      backgroundColor: '#f6ffed',
                      marginBottom: 16
                    }}
                  >
                    <div style={{ textAlign: 'center', marginBottom: 12 }}>
                      <Avatar src={searchResult.avatar} size={48} icon={<UserOutlined />} />
                    </div>
                    <div style={{ textAlign: 'center', marginBottom: 12 }}>
                      <div style={{ fontSize: '14px', fontWeight: 600, marginBottom: 4 }}>
                        {searchResult.name}
                        {searchResult.verified && <Tag color="blue" size="small" style={{ marginLeft: 4 }}>认证</Tag>}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.4', marginBottom: 8 }}>
                        {searchResult.description}
                      </div>

                      {/* 新增详细信息 */}
                      <div style={{ fontSize: '11px', color: '#999', marginBottom: 8 }}>
                        {searchResult.category && (
                          <Tag color="green" size="small" style={{ marginRight: 4 }}>
                            {searchResult.category}
                          </Tag>
                        )}
                        {searchResult.followers && (
                          <Tag color="orange" size="small" style={{ marginRight: 4 }}>
                            {searchResult.followers}
                          </Tag>
                        )}
                        {searchResult.location && (
                          <Tag color="purple" size="small">
                            {searchResult.location}
                          </Tag>
                        )}
                      </div>
                    </div>
                    <Button
                      type="primary"
                      block
                      icon={<PlusOutlined />}
                      onClick={handleAddAccount}
                    >
                      添加到监控
                    </Button>
                  </Card>
                )}

                <div style={{ padding: '16px 0', borderTop: '1px solid #f0f0f0' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    💡 支持搜索：人民日报、新华社、央视新闻等主流媒体公众号
                  </Text>
                </div>
              </Card>
            </Col>

            {/* 第二列：监控列表 */}
            <Col span={8}>
              <Card className="content-card" style={{ height: '100%', overflow: 'auto' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
                  <Title level={3} style={{ margin: 0, fontSize: '18px' }}>
                    <EyeOutlined /> 监控列表
                    <Badge count={accounts.length} style={{ marginLeft: 8 }} />
                  </Title>
                  <Button icon={<ReloadOutlined />} onClick={fetchAccounts} size="small">
                    刷新
                  </Button>
                </div>

                <Spin spinning={loading}>
                  {accounts.length === 0 ? (
                    <Empty
                      description="暂无监控的公众号"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ) : (
                    <List
                      className="monitor-list-container"
                      dataSource={accounts}
                      renderItem={(account) => (
                        <List.Item
                          className={`monitor-list-item ${selectedAccountId === account.id ? 'selected' : ''}`}
                          style={{
                            padding: selectedAccountId === account.id ? '14px 18px' : '16px 20px',
                            borderBottom: selectedAccountId === account.id ? 'none' : '1px solid #f0f0f0',
                            alignItems: 'flex-start',
                            backgroundColor: selectedAccountId === account.id ? '#f0f9ff' : 'transparent',
                            border: selectedAccountId === account.id ? '2px solid #1890ff' : '2px solid transparent',
                            borderRadius: '8px',
                            margin: '6px 12px',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            position: 'relative'
                          }}
                          onClick={() => handleSelectAccount(account.id)}
                          onMouseEnter={(e) => {
                            if (selectedAccountId !== account.id) {
                              e.currentTarget.style.backgroundColor = '#fafafa';
                              e.currentTarget.style.border = '2px solid #d9d9d9';
                              e.currentTarget.style.borderRadius = '8px';
                              e.currentTarget.style.padding = '14px 18px';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (selectedAccountId !== account.id) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.border = '2px solid transparent';
                              e.currentTarget.style.padding = '16px 20px';
                            }
                          }}
                        >
                          <div style={{ display: 'flex', width: '100%', gap: '12px' }}>
                            <Avatar src={account.avatar} size={40} icon={<UserOutlined />} />
                            <div style={{ flex: 1, minWidth: 0 }}>
                              <div style={{
                                fontSize: '14px',
                                fontWeight: selectedAccountId === account.id ? 700 : 600,
                                color: selectedAccountId === account.id ? '#1890ff' : '#333',
                                lineHeight: '1.4',
                                marginBottom: 6,
                                wordBreak: 'break-word'
                              }}>
                                {account.name}
                              </div>
                              <div style={{
                                fontSize: '12px',
                                color: '#666',
                                lineHeight: '1.4',
                                marginBottom: 8,
                                wordBreak: 'break-word'
                              }}>
                                {account.description}
                              </div>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Text type="secondary" style={{ fontSize: '11px' }}>
                                  {new Date(account.created_at).toLocaleDateString()}
                                </Text>
                                <div style={{ display: 'flex', gap: '2px' }}>
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<ReloadOutlined />}
                                    onClick={() => handleRefreshAccount(account.id)}
                                    title="刷新"
                                    style={{ padding: '2px 4px' }}
                                  />
                                  <Popconfirm
                                    title="确定删除？"
                                    onConfirm={() => handleDeleteAccount(account.id)}
                                    okText="确定"
                                    cancelText="取消"
                                  >
                                    <Button
                                      type="text"
                                      size="small"
                                      danger
                                      icon={<DeleteOutlined />}
                                      title="删除"
                                      style={{ padding: '2px 4px' }}
                                    />
                                  </Popconfirm>
                                </div>
                              </div>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  )}
                </Spin>
              </Card>
            </Col>

            {/* 第三列：最新文章 */}
            <Col span={9}>
              <Card className="content-card" style={{ height: '100%', overflow: 'auto' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
                  <Title level={3} style={{ margin: 0, fontSize: '18px' }}>
                    <FileTextOutlined /> 最新文章
                    <Badge count={articles.length} style={{ marginLeft: 8 }} />
                  </Title>
                  <Button icon={<ReloadOutlined />} onClick={fetchArticles} size="small">
                    刷新
                  </Button>
                </div>

                <Spin spinning={articlesLoading}>
                  {articles.length === 0 ? (
                    <Empty
                      description="暂无文章数据，请先添加公众号到监控列表"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ) : (
                    <List
                      dataSource={articles}
                      renderItem={(article) => (
                        <List.Item style={{
                          padding: '12px',
                          borderBottom: '1px solid #f0f0f0',
                          alignItems: 'flex-start'
                        }}>
                          <div style={{ display: 'flex', width: '100%', gap: '12px' }}>
                            {/* 文章封面图 */}
                            <div style={{
                              width: '80px',
                              height: '60px',
                              borderRadius: '6px',
                              overflow: 'hidden',
                              backgroundColor: '#f5f5f5',
                              flexShrink: 0,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              {article.cover_url ? (
                                <img
                                  src={article.cover_url}
                                  alt="文章封面"
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover'
                                  }}
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.parentNode.innerHTML = '<div style="color: #ccc; font-size: 12px;">暂无封面</div>';
                                  }}
                                />
                              ) : (
                                <FileTextOutlined style={{ fontSize: '20px', color: '#ccc' }} />
                              )}
                            </div>

                            {/* 文章信息 */}
                            <div style={{ flex: 1, minWidth: 0 }}>
                              <a
                                href={article.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  color: '#333',
                                  fontSize: '14px',
                                  fontWeight: 600,
                                  lineHeight: '1.4',
                                  display: 'block',
                                  marginBottom: '8px',
                                  textDecoration: 'none',
                                  wordBreak: 'break-word',
                                  transition: 'color 0.3s ease',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden'
                                }}
                                onMouseEnter={(e) => e.target.style.color = '#1890ff'}
                                onMouseLeave={(e) => e.target.style.color = '#333'}
                              >
                                {article.title}
                              </a>

                              <div style={{
                                fontSize: '12px',
                                color: '#999',
                                display: 'flex',
                                alignItems: 'center',
                                marginTop: 'auto'
                              }}>
                                <ClockCircleOutlined style={{ marginRight: 4 }} />
                                {formatTime(article.publish_time)}
                              </div>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  )}
                </Spin>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>

      {/* RSS订阅弹窗 */}
      <Modal
        title={
          <div>
            <RssOutlined style={{ marginRight: 8 }} />
            添加RSS订阅
          </div>
        }
        visible={rssModalVisible}
        onCancel={() => {
          setRssModalVisible(false);
          rssForm.resetFields();
          setRssValidationResult(null);
        }}
        footer={null}
        width={600}
      >
        <Tabs defaultActiveKey="manual">
          <TabPane tab="手动添加" key="manual">
            <Form
              form={rssForm}
              layout="vertical"
              onFinish={handleAddRSSSubscription}
            >
              <Form.Item
                label="RSS URL"
                name="rssUrl"
                rules={[
                  { required: true, message: '请输入RSS URL' },
                  { type: 'url', message: '请输入有效的URL' }
                ]}
              >
                <Input
                  placeholder="https://example.com/rss.xml"
                  prefix={<LinkOutlined />}
                  onChange={(e) => validateRSSUrl(e.target.value)}
                  suffix={
                    rssValidating ? (
                      <Spin size="small" />
                    ) : rssValidationResult ? (
                      rssValidationResult.valid ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      )
                    ) : null
                  }
                />
              </Form.Item>

              {rssValidationResult && !rssValidationResult.valid && (
                <div style={{ marginBottom: 16, padding: 8, backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 4 }}>
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    {rssValidationResult.error}
                  </Text>
                </div>
              )}

              {rssValidationResult && rssValidationResult.valid && (
                <div style={{ marginBottom: 16, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
                  <Text type="success" style={{ fontSize: '12px' }}>
                    RSS源验证成功！找到 {rssValidationResult.itemCount} 篇文章
                  </Text>
                </div>
              )}

              <Form.Item
                label="订阅名称"
                name="name"
                rules={[{ required: true, message: '请输入订阅名称' }]}
              >
                <Input placeholder="例如：阮一峰的网络日志" />
              </Form.Item>

              <Form.Item
                label="描述"
                name="description"
              >
                <Input.TextArea placeholder="订阅描述（可选）" rows={2} />
              </Form.Item>

              <Form.Item
                label="分类"
                name="category"
              >
                <Select placeholder="选择分类（可选）">
                  <Option value="技术">技术</Option>
                  <Option value="新闻">新闻</Option>
                  <Option value="科技">科技</Option>
                  <Option value="生活">生活</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    添加订阅
                  </Button>
                  <Button onClick={() => {
                    setRssModalVisible(false);
                    rssForm.resetFields();
                    setRssValidationResult(null);
                  }}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="推荐RSS源" key="recommended">
            <div style={{ maxHeight: 400, overflowY: 'auto' }}>
              {recommendedFeeds.map((feed, index) => (
                <Card
                  key={index}
                  size="small"
                  style={{ marginBottom: 12 }}
                  actions={[
                    <Button
                      type="link"
                      size="small"
                      onClick={() => handleUseRecommendedFeed(feed)}
                    >
                      使用此RSS源
                    </Button>
                  ]}
                >
                  <Card.Meta
                    title={
                      <div>
                        {feed.name}
                        <Tag color="blue" size="small" style={{ marginLeft: 8 }}>
                          {feed.category}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 4 }}>{feed.description}</div>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {feed.rssUrl}
                        </Text>
                      </div>
                    }
                  />
                </Card>
              ))}
            </div>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
}

export default App;
