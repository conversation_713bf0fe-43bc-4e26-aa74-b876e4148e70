// 直接测试搜索功能
const { searchAccounts, getCategories, getAccountsByCategory } = require('./server/real-accounts-data');

console.log('🔍 直接测试搜索功能...\n');

// 测试搜索功能
const testQueries = ['人民日报', '新华社', '央视新闻', '36氪', '科技', '新闻媒体'];

testQueries.forEach(query => {
  console.log(`搜索: "${query}"`);
  const results = searchAccounts(query);
  console.log(`结果数量: ${results.length}`);
  
  if (results.length > 0) {
    results.slice(0, 3).forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.name} (${result.category}) - ${result.followers}`);
    });
  } else {
    console.log('  无结果');
  }
  console.log('');
});

// 测试分类
console.log('📂 测试分类功能...');
const categories = getCategories();
console.log(`分类数量: ${categories.length}`);
console.log(`分类列表: ${categories.join(', ')}`);
console.log('');

// 测试按分类获取
console.log('📋 测试按分类获取...');
const newsAccounts = getAccountsByCategory('新闻媒体');
console.log(`新闻媒体分类下的公众号: ${newsAccounts.length}个`);
newsAccounts.forEach((account, index) => {
  console.log(`  ${index + 1}. ${account.name} - ${account.followers}`);
});
